import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

// 摄像头识别率统计表格列配置
export const columns: BasicColumn[] = [
  {
    title: '摄像头编号',
    dataIndex: 'cameraCode',
    align: 'center',
    width: 150,
  },
  {
    title: '摄像头名称',
    dataIndex: 'cameraName',
    align: 'center',
    width: 200,
  },
  {
    title: '所属企业',
    dataIndex: 'enterpriseName',
    align: 'center',
    width: 200,
  },
  {
    title: '安装位置',
    dataIndex: 'installLocation',
    align: 'center',
    width: 150,
  },
  {
    title: '总识别次数',
    dataIndex: 'totalRecognitions',
    align: 'center',
    width: 120,
    sorter: true,
  },
  {
    title: '成功识别次数',
    dataIndex: 'successRecognitions',
    align: 'center',
    width: 120,
    sorter: true,
  },
  {
    title: '失败识别次数',
    dataIndex: 'failedRecognitions',
    align: 'center',
    width: 120,
    sorter: true,
  },
  {
    title: '识别率(%)',
    dataIndex: 'recognitionRate',
    align: 'center',
    width: 120,
    sorter: true,
    customRender: ({ text }) => {
      const rate = parseFloat(text || 0);
      let color = '#52c41a'; // 绿色 - 优秀
      let status = '优秀';
      
      if (rate < 70) {
        color = '#ff4d4f'; // 红色 - 较差
        status = '较差';
      } else if (rate < 90) {
        color = '#faad14'; // 橙色 - 良好
        status = '良好';
      }
      
      return `<span style="color: ${color}; font-weight: bold;" title="${status}">${rate.toFixed(2)}%</span>`;
    },
  },
  {
    title: '最后识别时间',
    dataIndex: 'lastRecognitionTime',
    align: 'center',
    width: 180,
  },
  {
    title: '设备状态',
    dataIndex: 'deviceStatus',
    align: 'center',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        'online': { color: '#52c41a', text: '在线' },
        'offline': { color: '#ff4d4f', text: '离线' },
        'maintenance': { color: '#faad14', text: '维护中' }
      };
      const status = statusMap[text] || { color: '#d9d9d9', text: '未知' };
      return `<span style="color: ${status.color}; font-weight: bold;">${status.text}</span>`;
    },
  },
];

// 搜索表单配置
export const searchFormSchema: FormSchema[] = [
  {
    label: '时间段',
    field: 'timeRange',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: false,
    },
    colProps: { span: 8 },
  },
  {
    label: '企业',
    field: 'enterpriseCode',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'base_enterprise,name,code',
      async: true,
      pageSize: 20,
      placeholder: '请选择企业',
      allowClear: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '摄像头',
    field: 'cameraCode',
    component: 'Input',
    componentProps: {
      placeholder: '请输入摄像头编号或名称',
      allowClear: true,
    },
    colProps: { span: 6 },
  },
  {
    label: '设备状态',
    field: 'deviceStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '在线', value: 'online' },
        { label: '离线', value: 'offline' },
        { label: '维护中', value: 'maintenance' },
      ],
      placeholder: '请选择设备状态',
      allowClear: true,
    },
    colProps: { span: 4 },
  },
];

// 识别率等级配置
export const recognitionRateConfig = {
  excellent: { min: 90, color: '#52c41a', label: '优秀' },
  good: { min: 70, color: '#faad14', label: '良好' },
  poor: { min: 0, color: '#ff4d4f', label: '较差' },
};

// 统计卡片配置
export const statisticCards = [
  {
    title: '总识别次数',
    dataIndex: 'totalRecognitions',
    valueStyle: { color: '#3f8600' },
    prefix: '',
    suffix: '次',
  },
  {
    title: '成功识别次数',
    dataIndex: 'successRecognitions',
    valueStyle: { color: '#1890ff' },
    prefix: '',
    suffix: '次',
  },
  {
    title: '失败识别次数',
    dataIndex: 'failedRecognitions',
    valueStyle: { color: '#cf1322' },
    prefix: '',
    suffix: '次',
  },
  {
    title: '总识别率',
    dataIndex: 'recognitionRate',
    valueStyle: { color: '#722ed1' },
    prefix: '',
    suffix: '%',
    precision: 2,
  },
];

// 图表默认配置
export const defaultChartOption = {
  title: {
    text: '识别率趋势',
    textStyle: {
      fontSize: 16,
      fontWeight: 'normal'
    }
  },
  tooltip: {
    trigger: 'axis',
    formatter: function(params) {
      let result = params[0].name + '<br/>';
      params.forEach(function(item) {
        result += item.marker + item.seriesName + ': ' + item.value + '%<br/>';
      });
      return result;
    }
  },
  yAxis: {
    name: '识别率(%)',
    min: 0,
    max: 100
  }
};
