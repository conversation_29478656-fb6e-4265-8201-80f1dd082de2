<template>
    <a-descriptions title="通知详情" :column="2">
      <a-descriptions-item label="车牌号"  >{{ plate }}</a-descriptions-item>
      <a-descriptions-item label="地磅类型">{{ poundType }}</a-descriptions-item>
      <a-descriptions-item label="违法站点" span=2>{{ siteName }}</a-descriptions-item>
      <a-descriptions-item label="超载时间" span=2>{{ time }}</a-descriptions-item>
      <a-descriptions-item label="超载时间">{{ time }}</a-descriptions-item>
    </a-descriptions>
  </template>
  
  <script  lang="ts">
  import { defineComponent, defineProps } from 'vue';
  import {  Descriptions } from 'ant-design-vue';
  export default defineComponent({
    props: {
      plate: {
        type: String,
        default: '晋E00000',
      },
      siteName: {
        type: String,
        default: '冶底公路超限检测站',
      },
      time: {
        type: String,
        default: '2023-08-08 12:00:00',
      },
      poundType: {
        type: String,
        default: '静态磅',
      },
    },
    components: {
      [Descriptions.name]: Descriptions,
      [Descriptions.Item.name]: Descriptions.Item,
    },
    setup() {
    
    },
  });

  </script>
  
  <style scoped lang="less">
  .custom-notification {
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  </style>