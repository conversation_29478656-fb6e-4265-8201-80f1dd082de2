<template>
  <div class="integration-example">
    <a-card title="过磅数据弹框集成示例">
      <a-space direction="vertical" size="large" style="width: 100%">
        
        <!-- 基本使用示例 -->
        <a-card size="small" title="基本使用">
          <a-space>
            <a-button type="primary" @click="openBasicModal">
              基本弹框
            </a-button>
            <a-button @click="openCustomTitleModal">
              自定义标题
            </a-button>
          </a-space>
        </a-card>

        <!-- 在表格中集成示例 -->
        <a-card size="small" title="表格集成示例">
          <BasicTable @register="registerTable" :rowSelection="rowSelection">
            <template #tableTitle>
              <a-space>
                <a-button type="primary" @click="openWeightDataModal">
                  <Icon icon="ant-design:bar-chart-outlined" />
                  查看过磅数据
                </a-button>
                <a-button @click="openSelectedDataModal" :disabled="!hasSelected">
                  查看选中车辆数据
                </a-button>
              </a-space>
            </template>
            
            <template #action="{ record }">
              <a-space>
                <a-button type="link" size="small" @click="viewVehicleWeightData(record)">
                  过磅记录
                </a-button>
              </a-space>
            </template>
          </BasicTable>
        </a-card>

        <!-- 统计卡片集成示例 -->
        <a-card size="small" title="统计卡片集成">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card size="small" hoverable @click="openTodayDataModal">
                <a-statistic
                  title="今日过磅"
                  :value="statistics.todayCount"
                  suffix="次"
                  :value-style="{ color: '#3f8600' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small" hoverable @click="openOverloadDataModal">
                <a-statistic
                  title="超载车辆"
                  :value="statistics.overloadCount"
                  suffix="辆"
                  :value-style="{ color: '#cf1322' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small" hoverable @click="openWeekDataModal">
                <a-statistic
                  title="本周过磅"
                  :value="statistics.weekCount"
                  suffix="次"
                  :value-style="{ color: '#1890ff' }"
                />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card size="small" hoverable @click="openMonthDataModal">
                <a-statistic
                  title="本月过磅"
                  :value="statistics.monthCount"
                  suffix="次"
                  :value-style="{ color: '#722ed1' }"
                />
              </a-card>
            </a-col>
          </a-row>
        </a-card>

      </a-space>
    </a-card>

    <!-- 各种弹框实例 -->
    <WeightDataModal @register="registerBasicModal" />
    <WeightDataModal @register="registerCustomModal" title="企业过磅数据查询" />
    <WeightDataModal @register="registerWeightModal" title="站点过磅数据" />
    <WeightDataModal @register="registerSelectedModal" title="选中车辆过磅数据" />
    <WeightDataModal @register="registerVehicleModal" title="车辆过磅历史" />
    <WeightDataModal @register="registerTodayModal" title="今日过磅数据" />
    <WeightDataModal @register="registerOverloadModal" title="超载车辆数据" />
    <WeightDataModal @register="registerWeekModal" title="本周过磅数据" />
    <WeightDataModal @register="registerMonthModal" title="本月过磅数据" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, reactive } from 'vue';
import { BasicTable, useTable } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { Icon } from '/@/components/Icon';
import WeightDataModal from '../index.vue';
import { message } from 'ant-design-vue';

// 模拟统计数据
const statistics = reactive({
  todayCount: 156,
  overloadCount: 23,
  weekCount: 892,
  monthCount: 3456,
});

// 表格选择
const checkedKeys = ref<Array<string | number>>([]);
const hasSelected = computed(() => checkedKeys.value.length > 0);

const rowSelection = {
  type: 'checkbox',
  selectedRowKeys: checkedKeys,
  onChange: (selectedRowKeys: string[]) => {
    checkedKeys.value = selectedRowKeys;
  },
};

// 表格配置
const [registerTable] = useTable({
  title: '车辆管理',
  api: () => {
    // 模拟表格数据
    return Promise.resolve({
      items: [
        {
          id: '1',
          vehicleNo: '晋A12345',
          ownerName: '张三',
          phoneNumber: '13800138000',
          lastWeightTime: '2024-01-15 14:30:00',
        },
        {
          id: '2',
          vehicleNo: '晋B67890',
          ownerName: '李四',
          phoneNumber: '13900139000',
          lastWeightTime: '2024-01-15 15:45:00',
        },
      ],
      totalCount: 2,
    });
  },
  columns: [
    {
      title: '车牌号',
      dataIndex: 'vehicleNo',
      width: 120,
    },
    {
      title: '车主姓名',
      dataIndex: 'ownerName',
      width: 100,
    },
    {
      title: '联系电话',
      dataIndex: 'phoneNumber',
      width: 130,
    },
    {
      title: '最后过磅时间',
      dataIndex: 'lastWeightTime',
      width: 160,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      slots: { customRender: 'action' },
    },
  ],
  pagination: true,
  showTableSetting: true,
  tableSetting: { fullScreen: true },
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
});

// 注册各种弹框
const [registerBasicModal, { openModal: openBasicModal }] = useModal();
const [registerCustomModal, { openModal: openCustomModal }] = useModal();
const [registerWeightModal, { openModal: openWeightModal }] = useModal();
const [registerSelectedModal, { openModal: openSelectedModal }] = useModal();
const [registerVehicleModal, { openModal: openVehicleModal }] = useModal();
const [registerTodayModal, { openModal: openTodayModal }] = useModal();
const [registerOverloadModal, { openModal: openOverloadModal }] = useModal();
const [registerWeekModal, { openModal: openWeekModal }] = useModal();
const [registerMonthModal, { openModal: openMonthModal }] = useModal();

// 弹框操作方法
const openCustomTitleModal = () => {
  openCustomModal(true);
};

const openWeightDataModal = () => {
  openWeightModal(true);
};

const openSelectedDataModal = () => {
  if (!hasSelected.value) {
    message.warning('请先选择车辆');
    return;
  }
  openSelectedModal(true, {
    selectedVehicles: checkedKeys.value,
  });
};

const viewVehicleWeightData = (record: any) => {
  openVehicleModal(true, {
    vehicleNo: record.vehicleNo,
    vehicleId: record.id,
  });
};

// 统计卡片点击事件
const openTodayDataModal = () => {
  openTodayModal(true, {
    dateRange: 'today',
  });
};

const openOverloadDataModal = () => {
  openOverloadModal(true, {
    filterType: 'overload',
  });
};

const openWeekDataModal = () => {
  openWeekModal(true, {
    dateRange: 'week',
  });
};

const openMonthDataModal = () => {
  openMonthModal(true, {
    dateRange: 'month',
  });
};
</script>

<style lang="less" scoped>
.integration-example {
  padding: 20px;
  
  .ant-card {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .ant-statistic {
    cursor: pointer;
    
    &:hover {
      .ant-statistic-content {
        color: #1890ff !important;
      }
    }
  }
}
</style>
