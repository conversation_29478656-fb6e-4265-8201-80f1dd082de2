<template>
  <div class="p-4">
    <!-- 搜索表单 -->
    <a-card :bordered="true" style="margin-bottom: 16px">
      <a-form :form="form">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="时间段" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
              <JRangeDate 
                @change="dateRangeChange" 
                :RangeValue="searchParams.timeValue"
                v-model:value="searchParams.defaultValue" 
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="企业" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
              <JSearchSelect
                v-model:value="searchParams.enterpriseCode"
                dict="base_enterprise,name,code"
                :async="true"
                :pageSize="20"
                placeholder="请选择企业"
                allowClear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery">查询</a-button>
              <a-button type="primary" @click="searchReset" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 统计结果 -->
    <a-card :bordered="true">
      <div style="text-align: right; margin-bottom: 16px">
        <a-button v-print="'#printContent'" ghost type="primary">打印</a-button>
      </div>
      
      <div ref="print" id="printContent">
        <!-- 总识别率统计 -->
        <a-row style="margin-bottom: 24px">
          <a-col :span="24">
            <a-card title="企业总识别率统计" size="small">
              <a-row :gutter="16">
                <a-col :span="6" v-for="card in statisticCards" :key="card.dataIndex">
                  <a-statistic
                    :title="card.title"
                    :value="totalStats[card.dataIndex]"
                    :suffix="card.suffix"
                    :precision="card.precision"
                    :value-style="card.valueStyle"
                  />
                </a-col>
              </a-row>
            </a-card>
          </a-col>
        </a-row>

        <!-- 识别率趋势图 -->
        <a-row style="margin-bottom: 24px">
          <a-col :span="24">
            <a-card title="识别率趋势图" size="small">
              <SingleLine
                :option="chartOption"
                :chartData="chartData"
                height="300px"
              />
            </a-card>
          </a-col>
        </a-row>

        <!-- 摄像头识别率详情表格 -->
        <a-row>
          <a-col :span="24">
            <a-card title="各摄像头识别率统计" size="small">
              <BasicTable 
                @register="registerTable"
                :loading="tableLoading"
              />
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { JRangeDate, JSearchSelect } from '/@/components/Form';
import { BasicTable, useTable } from '/@/components/Table';
import SingleLine from '/@/components/chart/SingleLine.vue';
import { getCameraRecognitionRate, getEnterpriseRecognitionRate } from './PlateRecognitionRate.api';
import { columns, statisticCards, defaultChartOption } from './PlateRecognitionRate.data';
import moment from 'dayjs';
import { message } from 'ant-design-vue';

// 搜索参数
const searchParams = reactive({
  timeValue: [moment().subtract(7, 'days'), moment()],
  defaultValue: [moment().subtract(7, 'days'), moment()],
  enterpriseCode: '',
  startTime: moment().subtract(7, 'days').format('YYYY-MM-DD'),
  endTime: moment().format('YYYY-MM-DD'),
});

// 总统计数据
const totalStats = reactive({
  totalRecognitions: 0,
  successRecognitions: 0,
  failedRecognitions: 0,
  recognitionRate: 0,
});

// 图表数据
const chartData = ref([]);
const chartOption = ref(defaultChartOption);

// 表格配置
const tableLoading = ref(false);

const [registerTable, { setTableData }] = useTable({
  columns,
  dataSource: [],
  pagination: {
    pageSize: 20,
  },
  showIndexColumn: true,
  bordered: true,
  size: 'small',
});

// 表单引用
const form = ref();

// 时间范围改变
const dateRangeChange = (dates) => {
  if (dates && dates.length === 2) {
    searchParams.startTime = dates[0].format('YYYY-MM-DD');
    searchParams.endTime = dates[1].format('YYYY-MM-DD');
  }
};

// 查询
const searchQuery = async () => {
  await loadData();
};

// 重置
const searchReset = () => {
  searchParams.timeValue = [moment().subtract(7, 'days'), moment()];
  searchParams.defaultValue = [moment().subtract(7, 'days'), moment()];
  searchParams.enterpriseCode = '';
  searchParams.startTime = moment().subtract(7, 'days').format('YYYY-MM-DD');
  searchParams.endTime = moment().format('YYYY-MM-DD');
  loadData();
};

// 加载数据
const loadData = async () => {
  try {
    tableLoading.value = true;
    
    const params = {
      startTime: searchParams.startTime,
      endTime: searchParams.endTime,
      enterpriseCode: searchParams.enterpriseCode || undefined,
    };

    // 获取摄像头识别率数据
    const cameraResponse = await getCameraRecognitionRate(params);
    if (cameraResponse.success) {
      setTableData(cameraResponse.result || []);
    }

    // 获取企业总识别率数据
    const enterpriseResponse = await getEnterpriseRecognitionRate(params);
    if (enterpriseResponse.success) {
      const data = enterpriseResponse.result;
      Object.assign(totalStats, {
        totalRecognitions: data.totalRecognitions || 0,
        successRecognitions: data.successRecognitions || 0,
        failedRecognitions: data.failedRecognitions || 0,
        recognitionRate: data.recognitionRate || 0,
      });

      // 更新图表数据
      if (data.trendData && data.trendData.length > 0) {
        chartData.value = data.trendData.map(item => ({
          name: item.date,
          value: item.rate
        }));
      }
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败，请重试');
  } finally {
    tableLoading.value = false;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="less" scoped>
.p-4 {
  padding: 16px;
}

:deep(.ant-statistic-title) {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-statistic-content) {
  font-size: 20px;
  font-weight: 500;
}

:deep(.ant-card-head-title) {
  font-size: 16px;
  font-weight: 500;
}
</style>
