<template>
  <div class="plate" v-if="isPlate(plateNumber)" @click="copyText">
    <span :class="['plate-left', leftColor]">{{ splitPlate(plateNumber)[0] }}</span>
    <span :class="['plate-right', rightColor]">{{ splitPlate(plateNumber)[1] }}</span>
  </div>
  <div v-else>
    {{ plateNumber }}
  </div>
</template>
<script lang="ts">
  import { ref, watchEffect, defineComponent, unref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useCopyToClipboard } from '/@/hooks/web/useCopyToClipboard';
  export default defineComponent({
    name: 'PlateShow',
    props: {
      plateNumber: {
        type: String,
        default: '无车牌',
      },
      color: {
        type: Number,
        default: 0,
      },
    },
    setup(props) {
      const { createMessage } = useMessage();
      const { info, success, warning, error } = createMessage;
      const { clipboardRef, copiedRef } = useCopyToClipboard();
      const leftColor = ref('');
      const rightColor = ref('');

      watchEffect(async () => {
        convertColorToCss(props.color);
      });
      const copyText = async () => {
        clipboardRef.value = props.plateNumber;
        console.log(copiedRef);
        if (unref(copiedRef)) {
          info('复制成功！');
        }
      };

      //切分车牌
      function splitPlate(plate) {
        let plateHead = plate.slice(0, 2);
        let plateContent = plate.slice(2);
        return [plateHead, plateContent];
      }

      //转换颜色样式
      function convertColorToCss(color) {
        leftColor.value = '';
        rightColor.value = '';
        switch (color) {
          case 0:
            leftColor.value = 'plate-blue';
            rightColor.value = 'plate-blue';
            break;
          case 1:
            leftColor.value = 'plate-yellow';
            rightColor.value = 'plate-yellow';
            break;
          case 2:
            leftColor.value = 'plate-black';
            rightColor.value = 'plate-black';
            break;
          case 3:
            leftColor.value = 'plate-white';
            rightColor.value = 'plate-white';
            break;
          case 4:
            leftColor.value = 'plate-linerGreen';
            rightColor.value = 'plate-linerGreen';
            break;
          case 5:
            leftColor.value = 'plate-yellow';
            rightColor.value = 'plate-green';
            break;
          case 6:
            leftColor.value = 'plate-linerBlue';
            rightColor.value = 'plate-linerBlue';
            break;
          case 11:
            leftColor.value = 'plate-green';
            rightColor.value = 'plate-green';
            break;
          case 12:
            leftColor.value = 'plate-red';
            rightColor.value = 'plate-red';
            break;
          default:
            leftColor.value = 'plate-no';
            rightColor.value = 'plate-no';
        }
      }

      //是否车牌号
      function isPlate(plate) {
        if (plate !== null) {
          if (plate.length >= 6) {
            return true;
          }
        }
        return false;
      }

      return { isPlate, convertColorToCss, splitPlate, leftColor, rightColor, copyText };
    },
  });
</script>
<style scoped>
  .plate {
    width: 100%;
    height: 35px;
    border-radius: 5px;
    text-align: center;
    line-height: 35px;
    font-family: 微软雅黑;
    font-weight: bold;
    padding: 0 2px;
    box-sizing: border-box;
    cursor: pointer;
  }
  .plate-blue {
    border: 1px solid rgb(0, 0, 0);
    background: #3e0eec;
    color: white;
  }
  .plate-yellow {
    border: 1px solid rgb(0, 0, 0);
    background: #e4fd02;
  }
  .plate-black {
    border: 1px solid rgb(0, 0, 0);
    background: #181717;
    color: white;
  }
  .plate-white {
    border: 1px solid rgb(0, 0, 0);
    background: #faf9f9;
  }
  .plate-linerGreen {
    border: 1px solid rgb(0, 0, 0);
    background: linear-gradient(0, #61ff15, transparent);
  }

  .plate-red {
    border: 1px solid rgb(0, 0, 0);
    background: #fc0000;
  }
  .plate-green {
    border: 1px solid rgb(0, 0, 0);
    background: #20d12f;
  }
  .plate-linerBlue {
    border: 1px solid rgb(0, 0, 0);
    background: linear-gradient(0, #1519ff, #ffffff 30%, transparent);
  }

  .plate-no {
    border: 1px solid rgb(0, 0, 0);
    background: #ccc;
  }
  .plate-left {
    box-sizing: border-box;
    border-right: none;
    border-top-left-radius: 1px;
    border-bottom-left-radius: 1px;
    display: inline-block;
    width: 50px;
    padding-left: 7px;
    padding-right: 7px;
    position: relative;
  }
  .plate-right {
    box-sizing: border-box;
    border-left: none;
    border-top-right-radius: 1px;
    border-bottom-right-radius: 1px;
    display: inline-block;
    width: 75px;
  }
  .plate-left:after {
    content: '●';
    position: absolute;
    right: -2px;
    display: inline-block;
    width: 5px;
  }
</style>
