<template>
  <div class="container">
    <div class="container_box" style="background: #041451">
      <div class="header">
        <div class="title">本月各超限站超载数据统计图</div>
      </div>
      <div ref="myEchart" class="chart"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import echarts from '@/utils/lib/echarts';
import { getMonthSiteWeightStats } from '/@/views/dashboard/Analysis/api';

const myEchart = ref();
let chart: any;

const data = ref({
  stations: [] as string[], // 超限站名称
  overloadData: [] as number[], // 超载数据
});

const initChart = () => {
  chart = echarts.init(myEchart.value);
  updateChart();
};

const updateChart = () => {
  const option = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        const dataIndex = params[0].dataIndex;
        const fullStationName = data.value.stations[dataIndex];
        const value = params[0].value;
        return `${fullStationName}<br/>超载数量: ${value}辆`;
      }
    },
    grid: {
      left: '8%',
      right: '5%',
      bottom: '30%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.value.stations,
      axisLabel: {
        color: '#fff',
        fontSize: 10,
        rotate: 30,
        interval: 0,
        margin: 15,
        formatter: function(value: string) {
          // 如果站点名称太长，进行截断处理
          if (value.length > 8) {
            return value.substring(0, 6) + '...';
          }
          return value;
        }
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '超载数量(辆)',
      nameTextStyle: {
        color: '#fff',
        fontSize: 12
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.1)'
        }
      }
    },
    series: [
      {
        name: '超载数据',
        type: 'bar',
        data: data.value.overloadData,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ff6b6b' },
              { offset: 1, color: '#ff6b6b60' }
            ]
          }
        },
        emphasis: {
          itemStyle: {
            color: '#ff6b6b'
          }
        }
      }
    ]
  };

  chart.setOption(option);
};

// 调用真实接口获取数据
const fetchMonthSiteWeightStats = async () => {
  try {
    console.log('开始调用 /dashboard/monthSiteWeightStats 接口...');
    const result = await getMonthSiteWeightStats({});
    console.log('接口调用成功，返回数据:', result);

    // 检查返回数据结构
    if (result && result.result && Array.isArray(result.result) && result.result.length > 0) {
      parseRealData(result.result);
    } else if (result && Array.isArray(result) && result.length > 0) {
      parseRealData(result);
    } else {
      console.log('接口返回数据为空，使用模拟数据');
      generateMockData();
    }
  } catch (error) {
    console.error('接口调用失败:', error);
    console.log('使用模拟数据');
    generateMockData();
  }
};

// 解析真实数据
const parseRealData = (apiData: any[]) => {
  console.log('解析真实数据:', apiData);

  const stations: string[] = [];
  const overloadData: number[] = [];

  apiData.forEach(item => {
    // 假设 API 返回的数据包含站点名称和超载数量
    // 根据实际 API 返回的字段名调整
    stations.push(item.siteShortName || item.siteShortName || '未知站点');
    overloadData.push(item.overloadCount || 0);
  });

  data.value.stations = stations;
  data.value.overloadData = overloadData;

  console.log('解析后的数据:', {
    stations: data.value.stations,
    overloadData: data.value.overloadData
  });

  // 数据更新后重新渲染图表
  if (chart) {
    updateChart();
  }
};

const generateMockData = () => {
  // 模拟超限站数据
  const stations = [
    '晋城站', '高平站', '阳城站', '陵川站',
    '沁水站', '泽州站', '城区站', '开发区站'
  ];

  const overloadData: number[] = [];

  stations.forEach(() => {
    overloadData.push(Math.floor(Math.random() * 150) + 30);
  });

  data.value.stations = stations;
  data.value.overloadData = overloadData;
};

onMounted(() => {
  // 调用真实接口获取数据
  fetchMonthSiteWeightStats();
  initChart();

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize();
    }
  });
});
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
  
  .container_box {
    width: 95%;
    height: 95%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    box-shadow: rgba(128, 128, 128, 0.3) 0px 0px 40px inset;
    border: 2px solid rgba(128, 128, 128, 0.3);
    
    .header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 15px;

      .title {
        color: #fff;
        font-size: 15px;
        font-weight: 600;
      }
    }
    
    .chart {
      flex: 1;
      width: 100%;
    }
  }
}
</style>
