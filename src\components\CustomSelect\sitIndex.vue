<template>
    <Cascader v-bind="attrs" :value="store.selectsitData" :change-on-select="true" :options="getOptions"
        @change="handleChange" />
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { Cascader } from 'ant-design-vue';
import { useAttrs } from '/@/hooks/core/useAttrs';
import { useSelectsitData } from '@/store/modules/getMapData.ts'
import { jcdata } from '@/api/jcdata'
export default defineComponent({
    name: 'CustomSelect',
    components: {
        Cascader,
    },
    props: {
    },
    setup(props, { emit }) {
        const getOptions = ref<any[]>([])
        const attrs = useAttrs();
        const store = useSelectsitData()
        const handleChange = (e) => {
            store.setSelectsitData(e)
        }
        onMounted(() => {
            initValue()
        })

        // 转换函数
        function convertToTree(data) {
            const map = new Map();
            const tree = [];

            // 初始化 Map
            data.forEach(item => {
                map.set(item.value, { ...item, children: [] });
            });

            // 构建树
            data.forEach(item => {
                const node = map.get(item.value);
                if (item.parent_code && map.has(item.parent_code)) {
                    const parent = map.get(item.parent_code);
                    parent.children.push(node);
                } else {
                    tree.push(node);
                }
            });

            // 移除空的 children 数组
            function removeEmptyChildren(node) {
                if (node.children.length === 0) {
                    delete node.children;
                } else {
                    node.children.forEach(child => removeEmptyChildren(child));
                }
            }

            tree.forEach(root => removeEmptyChildren(root));

            return tree;
        }
        const initValue = () => {
            const treeData = convertToTree(jcdata);
            getOptions.value =treeData
        }




        return {
            getOptions,
            handleChange,
            attrs,
            store
        }
    }
})
</script>