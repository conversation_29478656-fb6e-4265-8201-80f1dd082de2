<template>
  <div class="container">
    <div class="container_box" style="background:#041451">
      <div style="box-sizing: border-box; padding: 10px 5px 0px 10px; height: 80%">
        <div class="header" style="color:white">严重失信行为统计</div>
        <dv-capsule-chart :config="data.config" style="width: 100%; height: 100%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { getBlackStatisticsData } from '@/views/bigscreen/bigScreen.api';

const data = ref({
  config: {
    data: [],
    colors: ['#e062ae', '#fb7293', '#e690d1', '#32c5e9', '#96bfff'],
    unit: '个',
    showValue: true,
  },
});

onMounted(() => {
  getData();
});

const getData = () => {
  getBlackStatisticsData().then((res) => {
    // Replace the entire config object to ensure reactivity
    data.value.config = {
      ...data.value.config,
      data: res.result,
    };

  });
};
</script>

<style lang="less" scoped></style>
