/**
 * 过磅数据弹框组件类型定义
 */

/**
 * 过磅数据记录接口
 */
export interface WeightDataRecord {
  id: string;
  checkNo: string;
  siteName: string;
  vehicleNo: string;
  plateColor: string;
  total: number;
  limitWeight: number;
  overWeight: number;
  overRate: string;
  isOverload: number;
  checkTime: string;
  checkType: string;
  poundType: string;
  axles: number;
  axleType: string;
  distCode?: string;
  laneNumber?: string;
  equipCode?: string;
  vehicleAxlesType?: string;
  length?: number;
  width?: number;
  height?: number;
}

/**
 * 查询参数接口
 */
export interface WeightDataQueryParams {
  checkTime_begin?: string;
  checkTime_end?: string;
  siteName?: string;
  vehicleNo?: string;
  isOverload?: number;
  pageNo?: number;
  pageSize?: number;
  column?: string;
  order?: string;
}

/**
 * 分页配置接口
 */
export interface PaginationConfig {
  current: number;
  pageSize: number;
  total: number;
  showSizeChanger: boolean;
  showQuickJumper: boolean;
  showTotal: (total: number) => string;
}

/**
 * 表格列配置接口
 */
export interface TableColumn {
  title: string;
  dataIndex: string;
  width?: number;
  ellipsis?: boolean;
  fixed?: 'left' | 'right';
  slots?: {
    customRender: string;
  };
  render?: (text: any, record: WeightDataRecord) => any;
}

/**
 * 组件属性接口
 */
export interface WeightDataModalProps {
  title?: string;
}

/**
 * 组件事件接口
 */
export interface WeightDataModalEmits {
  (e: 'register', modal: any): void;
  (e: 'ok'): void;
  (e: 'cancel'): void;
}
