<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect,computed } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { cloneDeep } from 'lodash-es';
  export default defineComponent({
    name: 'BarIndex',
    props: {
      chartData: {
        type: Array,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: 'calc(100vh - 78px)',
      },
      // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
      seriesColor: {
        type: String,
        default: '#1890ff',
      },
      // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
    },
    setup(props) {
      console.log(props,'==>propsthisis')
      const chartRef = ref<HTMLDivElement | null>(null);
        const boolChartData =computed(()=>{
        return !(props.chartData.length ===0)
      })
      const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>);
      const option = reactive({
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%', // 增加底部边距，为竖直显示的企业名称留出空间
          top: '10%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              show: false,
              backgroundColor: '#333',
            },
          },
        },
        xAxis: {
          type: 'category',
          data: [],
          show: true,
          axisLabel: {
            rotate: 0, // 不旋转文字
            interval: 0, // 显示所有标签
            formatter: function(value) {
              // 将企业名称转换为竖直显示，每个字符占一行
              return value.split('').join('\n');
            },
            textStyle: {
              fontSize: 12,
              color: '#666',
              lineHeight: 16
            }
          }
        },
        yAxis: {
          type: 'value',
          show:true
        },
        series: [
          {
            name: '超载数',
            type: 'bar',
            data: [],
            color: props.seriesColor,
          },
        ],
        graphic:{
          type:'text',
          left:'center',
          top:'middle',
          silent:true,
          invisible:boolChartData.value,
          style:{
            text:'暂无数据',
            fill:'#999',
            font:'14px Microsoft YaHei',
            fontSize:'26px',
          }

        }
      });

      watchEffect(() => {
        props.chartData.length>0 && initCharts();
      });

      function initCharts() {
        console.log(props.chartData,'====>props.chartData')
        if (props.option) {
          Object.assign(option, cloneDeep(props.option));
        }

        // 过滤掉未超载的企业（只显示超载数大于0的企业）
        const filteredData = props.chartData.filter(item => item.value > 0);

        let seriesData = filteredData.map((item) => {
          return item.value;
        });
        let xAxisData = filteredData.map((item) => {
          return item.name;
        });
        option.series[0].data = seriesData;
        // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
        option.series[0].color = props.seriesColor;
        // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
        option.xAxis.data = xAxisData;
        option.graphic.invisible = !(filteredData.length === 0);
        console.log(option,'===》option')
        setOptions(option);
      }
      return { chartRef };
    },
  });
</script>
