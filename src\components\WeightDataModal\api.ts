import { defHttp } from '/@/utils/http/axios';

/**
 * 过磅数据查询API接口
 */
enum Api {
  // 查询过磅数据列表
  list = '/weight/checkSiteData/list',
  // 导出过磅数据
  exportXls = '/weight/checkSiteData/exportXls',
  // 获取过磅数据详情
  detail = '/weight/checkSiteData/detail',
  // 获取企业月度统计数据
  enterpriseMonthStat = '/stat/enterprise/month',
}

/**
 * 查询过磅数据参数接口
 */
export interface WeightDataQueryParams {
  // 开始时间
  checkTime_begin?: string;
  // 结束时间
  checkTime_end?: string;
  // 站点名称
  siteName?: string;
  // 车牌号
  vehicleNo?: string;
  // 是否超载 (1:超载, 0:正常)
  isOverload?: number;
  // 企业ID
  enterpriseId?: string;
  // 企业名称
  enterpriseName?: string;
  // 页码
  pageNo?: number;
  // 每页大小
  pageSize?: number;
  // 排序字段
  column?: string;
  // 排序方式
  order?: string;
}

/**
 * 地磅车头车尾统计
 */
export interface StatPoundVo {
  type: string; // 车头/车尾
  checkNum: number;
  checkRate: number;
}

/**
 * 企业出入口统计数据
 */
export interface StatEnterpriseOfDoorVo {
  enterpriseCode: string;
  carNum: number;
  type: string; // 入厂/出厂
  checkNum: number;
  checkRate: number;
  doorCode: string; // 出入口编码
  doorName: string; // 出入口名称
  doorCameraCode: string; // 出入口设备编码
  doorCameraName: string; // 出入口设备名称
  checkTime: string;
}

/**
 * 企业地磅统计数据
 */
export interface StatEnterpriseOfPoundVo {
  enterpriseCode: string;
  carNum: number;
  statPoundVos: StatPoundVo[]; // 车头/车尾统计
  completeRate: number; // 完整率
  poundCode: string; // 地磅设备编码
  poundName: string; // 地磅名称
  poundEquName: string; // 设备名称
  checkTime: string;
}

/**
 * 按日期分组的统计数据
 */
export interface StatEnterpriseByDateVo {
  statDate: string;
  doorDeviceStats: StatEnterpriseOfDoorVo[]; // 修正字段名
  poundDeviceStats: StatEnterpriseOfPoundVo[]; // 修正字段名
}

/**
 * 30天汇总统计
 */
export interface StatEnterpriseSummaryVo {
  totalCarNum: number; // 30天总过车数
  totalCheckNum: number; // 30天总识别数
  totalCheckRate: number; // 30天整体识别率
  totalTimelyRate: number; // 30天整体及时率
  totalCompleteRate: number; // 30天整体完整率（地磅）
  totalDoorDevices: number; // 出入口设备总数
  totalPoundDevices: number; // 地磅设备总数
  activeDays: number; // 有数据的天数
  dataCompleteness: number; // 数据完整度（有数据天数/总天数）
}

/**
 * 企业30天统计数据（新结构）
 */
export interface StatEnterpriseOfMonthVo {
  startDate: string; // 统计开始日期
  endDate: string; // 统计结束日期
  dailyStats: StatEnterpriseByDateVo[]; // 按日期分组的统计数据列表
  summaryStats: StatEnterpriseSummaryVo; // 30天汇总统计
}

/**
 * 过磅数据记录接口（保留原有结构用于详细数据展示）
 */
export interface WeightDataRecord {
  id: string;
  checkNo: string;
  siteName: string;
  vehicleNo: string;
  plateColor: string;
  total: number;
  limitWeight: number;
  overWeight: number;
  overRate: string;
  isOverload: number;
  checkTime: string;
  checkType: string;
  poundType: string;
  axles: number;
  axleType: string;
  distCode?: string;
  laneNumber?: string;
  equipCode?: string;
  vehicleAxlesType?: string;
  length?: number;
  width?: number;
  height?: number;
}

/**
 * API响应接口
 */
export interface WeightDataResponse {
  success: boolean;
  message: string;
  code: number;
  result: {
    records: WeightDataRecord[];
    total: number;
    size: number;
    current: number;
    pages: number;
  };
}

/**
 * 查询过磅数据列表
 * @param params 查询参数
 */
export const getWeightDataList = (params: WeightDataQueryParams) =>
  defHttp.get<WeightDataResponse>({ url: Api.list, params });

/**
 * 导出过磅数据
 * @param params 查询参数
 */
export const exportWeightData = (params: WeightDataQueryParams) =>
  defHttp.get({ url: Api.exportXls, params, responseType: 'blob' });

/**
 * 获取过磅数据详情
 * @param id 记录ID
 */
export const getWeightDataDetail = (id: string) =>
  defHttp.get<{ success: boolean; result: WeightDataRecord }>({
    url: Api.detail,
    params: { id }
  });

/**
 * 获取企业月度统计数据
 * @param enterpriseCode 企业编码
 */
export const getEnterpriseMonthStat = (enterpriseCode: string) =>
  defHttp.get<{ success: boolean; result: StatEnterpriseOfMonthVo }>({
    url: Api.enterpriseMonthStat,
    params: { enterpriseCode }
  });

/**
 * 生成企业月度统计模拟数据
 */
export const generateMockEnterpriseMonthStat = (enterpriseCode: string, enterpriseName: string): StatEnterpriseOfMonthVo => {
  const isCoalMine = enterpriseName?.includes('煤') || enterpriseName?.includes('矿');

  // 生成30天的出入口数据
  const doorData: StatEnterpriseOfDoorVo[] = [];
  const poundData: StatEnterpriseOfPoundVo[] = [];

  for (let i = 29; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];

    // 出入口数据（入厂/出厂）
    const baseCarNum = isCoalMine ? 80 : 50;
    const randomFactor = 0.7 + Math.random() * 0.6;

    ['入厂', '出厂'].forEach((type, index) => {
      const carNum = Math.floor(baseCarNum * randomFactor);
      const checkNum = Math.floor(carNum * (0.85 + Math.random() * 0.1)); // 85-95%识别率
      const checkRate = Number((checkNum / carNum * 100).toFixed(2));

      doorData.push({
        enterpriseCode,
        carNum,
        type,
        checkNum,
        checkRate,
        doorCode: `DOOR_${enterpriseCode}_${index + 1}`,
        doorName: `${type}口${index + 1}`,
        doorCameraCode: `CAM_${enterpriseCode}_${index + 1}`,
        doorCameraName: `${type}口摄像头${index + 1}`,
        checkTime: dateStr
      });
    });

    // 地磅数据
    const poundCarNum = Math.floor(baseCarNum * 0.8 * randomFactor); // 地磅数量少于出入口
    const completeRate = Number((88 + Math.random() * 10).toFixed(2)); // 88-98%完整率

    // 车头车尾统计
    const statPoundVos: StatPoundVo[] = [];
    ['车头', '车尾'].forEach(type => {
      const baseCheckNum = Math.floor(Math.random() * 50) + 10; // 10-60的识别数
      const checkNum = baseCheckNum;
      const checkRate = Number((85 + Math.random() * 10).toFixed(2)); // 85-95%识别率

      statPoundVos.push({
        type,
        checkNum,
        checkRate
      });
    });

    poundData.push({
      enterpriseCode,
      carNum: poundCarNum,
      statPoundVos,
      completeRate,
      poundCode: `POUND_${enterpriseCode}_1`,
      poundName: `地磅1`,
      poundEquName: `地磅设备1`,
      checkTime: dateStr
    });
  }

  // 生成质量指标
  const weighTimelyRate = Number((92 + Math.random() * 6).toFixed(2)); // 92-98%
  const weighCompleteRate = Number((88 + Math.random() * 8).toFixed(2)); // 88-96%
  const weighAccurateRate = Number((94 + Math.random() * 4).toFixed(2)); // 94-98%

  // 生成按日期分组的数据
  const dailyStats: StatEnterpriseByDateVo[] = [];
  for (let i = 29; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];

    // 为每一天生成部分出入口和地磅数据
    const dayDoorData = doorData.filter(item => item.checkTime === dateStr);
    const dayPoundData = poundData.filter(item => item.checkTime === dateStr);

    if (dayDoorData.length > 0 || dayPoundData.length > 0) {
      dailyStats.push({
        statDate: dateStr,
        doorDeviceStats: dayDoorData,
        poundDeviceStats: dayPoundData
      });
    }
  }

  // 计算汇总统计
  const totalCarNum = doorData.reduce((sum, item) => sum + item.carNum, 0);
  const totalCheckNum = doorData.reduce((sum, item) => sum + item.checkNum, 0);
  const totalCheckRate = totalCarNum > 0 ? Number((totalCheckNum / totalCarNum * 100).toFixed(2)) : 0;

  const summaryStats: StatEnterpriseSummaryVo = {
    totalCarNum,
    totalCheckNum,
    totalCheckRate, // 30天整体识别率
    totalTimelyRate: weighTimelyRate, // 30天整体及时率
    totalCompleteRate: weighCompleteRate, // 30天整体完整率（地磅）
    totalDoorDevices: 4, // 出入口设备总数
    totalPoundDevices: 2, // 地磅设备总数
    activeDays: dailyStats.length, // 有数据的天数
    dataCompleteness: Number((dailyStats.length / 30 * 100).toFixed(2)) // 数据完整度
  };

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 29);
  const endDate = new Date();

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
    dailyStats,
    summaryStats
  };
};

/**
 * 生成模拟数据（开发测试用）
 */
export const generateMockWeightData = (params: WeightDataQueryParams): WeightDataResponse => {
  const { pageNo = 1, pageSize = 10 } = params;
  
  // 生成模拟数据
  const allRecords: WeightDataRecord[] = [];
  for (let i = 1; i <= 100; i++) {
    const isOverload = Math.random() > 0.7 ? 1 : 0;
    const total = Math.floor(Math.random() * 50000) + 10000;
    const limitWeight = 25000;
    const overWeight = isOverload ? Math.floor(Math.random() * 10000) + 1000 : 0;
    const overRate = isOverload ? ((overWeight / limitWeight) * 100).toFixed(1) : '0';
    
    allRecords.push({
      id: `${i}`,
      checkNo: `CZ${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(i).padStart(4, '0')}`,
      siteName: `治超站${(i % 5) + 1}`,
      vehicleNo: `晋A${String(Math.floor(Math.random() * 90000) + 10000)}`,
      plateColor: Math.random() > 0.8 ? '黄' : '蓝',
      total,
      limitWeight,
      overWeight,
      overRate,
      isOverload,
      checkTime: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString().replace('T', ' ').substring(0, 19),
      checkType: Math.random() > 0.5 ? '动态' : '静态',
      poundType: Math.random() > 0.5 ? '动态磅' : '静态磅',
      axles: Math.floor(Math.random() * 4) + 2,
      axleType: `${Math.floor(Math.random() * 4) + 2}轴`,
      distCode: `1401${String((i % 10) + 1).padStart(2, '0')}`,
      laneNumber: `${Math.floor(Math.random() * 3) + 1}`,
      equipCode: `EQ${String(i).padStart(3, '0')}`,
      vehicleAxlesType: `${Math.floor(Math.random() * 4) + 2}轴货车`,
      length: Math.floor(Math.random() * 5000) + 8000,
      width: Math.floor(Math.random() * 1000) + 2000,
      height: Math.floor(Math.random() * 1500) + 2500,
    });
  }
  
  // 应用过滤条件
  let filteredRecords = allRecords;

  if (params.vehicleNo) {
    filteredRecords = filteredRecords.filter(record =>
      record.vehicleNo.includes(params.vehicleNo!)
    );
  }

  if (params.siteName) {
    filteredRecords = filteredRecords.filter(record =>
      record.siteName.includes(params.siteName!)
    );
  }

  if (params.enterpriseName) {
    // 模拟企业过磅数据筛选
    // 实际项目中这里会根据企业ID或名称进行数据库查询
    filteredRecords = filteredRecords.filter(record => {
      // 模拟：假设车牌号前缀与企业相关
      const enterprisePrefix = params.enterpriseName!.includes('煤矿') ? '煤' : '货';
      return record.vehicleNo.includes('A') || record.vehicleNo.includes('B');
    });
  }

  if (params.isOverload !== undefined) {
    filteredRecords = filteredRecords.filter(record =>
      record.isOverload === params.isOverload
    );
  }
  
  // 分页
  const start = (pageNo - 1) * pageSize;
  const end = start + pageSize;
  const records = filteredRecords.slice(start, end);
  
  return {
    success: true,
    message: '查询成功',
    code: 200,
    result: {
      records,
      total: filteredRecords.length,
      size: pageSize,
      current: pageNo,
      pages: Math.ceil(filteredRecords.length / pageSize),
    },
  };
};

/**
 * 获取企业30天数据质量率
 */
export const getEnterpriseQualityRate30DaysOptimized = (enterpriseCode: string) => {
  return defHttp.get({
    url: '/statistics/enterprise/getEnterpriseQualityRate30DaysOptimized',
    params: { enterpriseCode }
  });
};

/**
 * 获取企业每日设备数据
 */
export const getEnterpriseDailyDeviceData = (enterpriseCode: string, queryDate?: string) => {
  // 如果没有传入日期，默认使用今天
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
  const dateParam = queryDate || today;

  return defHttp.get({
    url: '/statistics/enterprise/getEnterpriseDailyDeviceData',
    params: {
      enterpriseCode,
      queryDate: dateParam
    }
  });
};
