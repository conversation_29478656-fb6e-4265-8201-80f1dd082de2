<template>
  <div class="test-container">
    <a-card title="企业过磅数据统计组件测试">
      <a-space>
        <a-button type="primary" @click="openModal">
          测试企业过磅数据统计
        </a-button>
        <a-button @click="openModalWithData">
          带企业信息测试
        </a-button>
      </a-space>
    </a-card>

    <!-- 企业过磅数据统计弹框 -->
    <EnterpriseWeightModal @register="registerModal" />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal';
import EnterpriseWeightModal from './EnterpriseWeightModal.vue';

// 注册弹框
const [registerModal, { openModal: openModalFn }] = useModal();

// 基本测试
const openModal = () => {
  openModalFn(true, {
    enterpriseId: 'test_001',
    enterpriseName: '测试企业有限公司',
    enterpriseCode: 'TEST001',
  });
};

// 带企业信息测试
const openModalWithData = () => {
  openModalFn(true, {
    enterpriseId: 'coal_mine_001',
    enterpriseName: '山西煤炭集团有限公司',
    enterpriseCode: 'COAL001',
  });
};
</script>

<style lang="less" scoped>
.test-container {
  padding: 20px;
}
</style>
