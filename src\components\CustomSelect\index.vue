<template>
    <Cascader
      v-bind="filteredAttrs"
      :value="store.selectData"
      :change-on-select="true"
      :options="getOptions"
      @change="handleChange"
    />
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, computed } from 'vue';
import { Cascader } from 'ant-design-vue';
import { useAttrs } from '/@/hooks/core/useAttrs';
import {useSelectData} from '@/store/modules/getMapData.ts'
import { jcdata } from '@/api/jcdata'
export default defineComponent({
    name: 'CustomSelect',
    components: {
        Cascader,
    },
    emits: ['update:saveCode'],
  props: {
    saveCode: { type: String, default: '' },
  },
    setup(props, { emit }) {
        console.log(props,'===>',props.saveCode,)
        const getOptions = ref<any[]>([])
        const attrs = useAttrs();
        const store = useSelectData()

        // 过滤掉可能导致冲突的属性
        const filteredAttrs = computed(() => {
            const { onChange, ...rest } = attrs;
            return rest;
        });

        const handleChange = (e) => {
            store.setSelectData(e)

        }
        onMounted(() => {
            initValue()
        })
             // 转换函数
             function convertToTree(data) {
            const map = new Map();
            const tree = [];

            // 初始化 Map
            data.forEach(item => {
                map.set(item.value, { ...item, children: [] });
            });

            // 构建树
            data.forEach(item => {
                const node = map.get(item.value);
                if (item.parent_code && map.has(item.parent_code)) {
                    const parent = map.get(item.parent_code);
                    parent.children.push(node);
                } else {
                    tree.push(node);
                }
            });

            // 移除空的 children 数组
            function removeEmptyChildren(node) {
                if (node.children.length === 0) {
                    delete node.children;
                } else {
                    node.children.forEach(child => removeEmptyChildren(child));
                }
            }

            tree.forEach(root => removeEmptyChildren(root));

            return tree;
        }
        const initValue = () => {
            const treeData = convertToTree(jcdata);
            console.log(treeData,'====>thisis treeData')
            getOptions.value =treeData
        }




        return {
            getOptions,
            handleChange,
            filteredAttrs,
            store
        }
    }
})
</script>