/**
 * 生成外部配置文件，用于生产发布后配置，无需重新打包
 */
import { GLOB_CONFIG_FILE_NAME, OUTPUT_DIR } from '../constant';
import fs, { writeFileSync } from 'fs-extra';
import { getEnvConfig, getRootPath } from '../utils';
import { getConfigFileName } from '../getConfigFileName';

interface CreateConfigParams {
  configName: string;
  config: any;
  configFileName?: string;
}

function createConfig(params: CreateConfigParams) {
  const { configName, config, configFileName } = params;
  try {
    const windowConf = `window.${configName}`;
    // Ensure that the variable will not be modified
    let configStr = `${windowConf}=${JSON.stringify(config)};`;
    configStr += `
      Object.freeze(${windowConf});
      Object.defineProperty(window, "${configName}", {
        configurable: false,
        writable: false,
      });
    `.replace(/\s/g, '');
    const buildEvent = process.env.npm_lifecycle_event;
    let path = '';
    switch (buildEvent) {
      case 'build:gaopingdev':
        path = `/gaopingproduction`;
        break;
      case 'build:chengqudev':
        path = `/chengquproduction`;
        break;
      case 'build:yangchengdev':
        path = `/yangchengproduction`;
        break;
      case 'build:qinshuidev':
        path = `/qinshuiproduction`;
        break;
      case 'build:lingchuandev':
        path = `/lingchuanproduction`;
        break;
      case 'build:zezhoudev':
        path = `/zezhouproduction`;
        break;
      case 'build':
        path = `/production`;
        break;
    }

    fs.mkdirp(getRootPath(OUTPUT_DIR));
    writeFileSync(getRootPath(`${OUTPUT_DIR}${path}/${configFileName}`), configStr);
  } catch (error) {
  }
}

export function runBuildConfig() {
  const config = getEnvConfig();
  const configFileName = getConfigFileName(config);
  createConfig({ config, configName: configFileName, configFileName: GLOB_CONFIG_FILE_NAME });
}
