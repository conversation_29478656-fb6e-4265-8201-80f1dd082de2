<template>
  <Drawer :open="open" @close="onClose" :destroyOnClose="true" :title="title" width="55%">
    <div v-if="imgList.length > 0">
      <ImagePreview :imageList="imgList" />
      <a-divider style="height: 1px; background-color: #575757" />
    </div>
    <Description
      size="small"
      title="具体信息"
      :column="2"
      :schema="detailSchema"
      :data="detailData"
      :collapseOptions="{ canExpand: true, helpMessage: '检测信息' }"
    />
    <a-table v-if="column.length > 0" :columns="column" :dataSource="dataSource" :pagination="false">
      <template v-if="props.head" #title>{{ props.head }}</template>
    </a-table>
    <NewMap v-if='detailData.longitude' :longitude='detailData.longitude' :latitude='detailData.latitude'/>
  </Drawer>
</template>

<script lang="ts" setup>
  import { Drawer, message } from 'ant-design-vue';
  import { ref, Ref } from 'vue';
  import NewMap from '../NewMap/index.vue'
  import { Description } from '/@/components/Description/index';
  import { ImagePreview } from '/@/components/Preview/index';
  import { ImageProps } from '/@/components/Preview/src/typing';
  // 定义 column 的类型（根据实际情况调整）
  interface ColumnType {
    key: string;
    title: string;
  }
  interface OtherType {
    titleValue?: string; // 可选的字符串
    columnValue?: ColumnType[]; // 可选的 ColumnType 对象
    tableApi?: string; // 可选的字符串
    imgListValue?: Array<Object>;
    detailSchemaValue: Array<Object>;
  }
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const props = defineProps(['head']);
  const detailData = ref({});
  const title = ref('');
  const column = ref([]);
  const dataSource = ref([]);
  const imgList = ref([]);
  const detailSchema = ref([]);
  const open = ref(false);
  const api = ref('');

  const openDrawer = (record, other: OtherType) => {
    const { titleValue, columnValue, tableApi, imgListValue, detailSchemaValue } = other;
    open.value = true;
    detailData.value = record;
    console.log(detailData.value,'===>detailData')
    // 处理 titleValue，设置标题或使用默认值
    if (titleValue) {
      title.value = titleValue;
    }
    if (detailSchemaValue) {
      detailSchema.value = detailSchemaValue;
    }
    if (columnValue) {
      column.value = columnValue || [];
    } else {
      column.value = [];
    }
    // 设置 Table API 地址，使用 null if tableApi 为空
    if (tableApi) {
      api.value = tableApi;
    } else {
      api.value = '';
    }
    if (imgListValue && imgListValue.length > 0) {
      imgList.value = imgListValue;
    }

    getTableList();
  };

  const getTableList = async () => {
    const res = await api.value;
    // if (res.code != 200) {
    //     message.error(`查询表格失败`)
    //     return
    // }
    dataSource.value = res.records || [];
  };

  const onClose = () => {
    open.value = false;
    imgList.value =[]
  };

  defineExpose({
    openDrawer,
    onClose,
  });
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
