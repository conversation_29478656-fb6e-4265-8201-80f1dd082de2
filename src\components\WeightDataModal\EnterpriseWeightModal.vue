<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="modalTitle"
    :width="1400"
    :min-height="700"
    :can-fullscreen="true"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="enterprise-weight-modal">
      <!-- 数据质量指标区域 -->
      <div class="quality-section">
        <a-row :gutter="16">
          <!-- 数据质量指标卡片 -->
          <a-col :span="24">
            <a-card size="small" title="30天数据质量指标" class="quality-metrics-card">
              <template #extra>
                <a-tooltip title="数据质量综合评估">
                  <Icon icon="ant-design:info-circle-outlined" />
                </a-tooltip>
              </template>

              <a-row :gutter="24">
                <!-- 称重数据及时率饼图 -->
                <a-col :span="8">
                  <div class="pie-chart-container">
                    <div class="chart-title">
                      <Icon icon="ant-design:clock-circle-outlined" style="margin-right: 8px; color: #52c41a;" />
                      称重数据及时率
                    </div>
                    <div ref="timelyRateChart" class="pie-chart"></div>
                  </div>
                </a-col>

                <!-- 称重数据完整率饼图 -->
                <a-col :span="8">
                  <div class="pie-chart-container">
                    <div class="chart-title">
                      <Icon icon="ant-design:check-circle-outlined" style="margin-right: 8px; color: #faad14;" />
                      称重数据完整率
                    </div>
                    <div ref="completeRateChart" class="pie-chart"></div>
                  </div>
                </a-col>

                <!-- 整体识别率饼图 -->
                <a-col :span="8">
                  <div class="pie-chart-container">
                    <div class="chart-title">
                      <Icon icon="ant-design:aim-outlined" style="margin-right: 8px; color: #722ed1;" />
                      整体识别率
                    </div>
                    <div ref="accurateRateChart" class="pie-chart"></div>
                  </div>
                </a-col>
              </a-row>

              <a-divider style="margin: 12px 0;" />

              <!-- 基础统计数据 -->
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-statistic
                    title="总过车次数"
                    :value="statisticsData.totalCount"
                    suffix="次"
                    :value-style="{ color: '#1890ff', fontSize: '16px' }"
                  />
                </a-col>
                <a-col :span="12">
                  <a-statistic
                    title="识别次数"
                    :value="statisticsData.overloadCount"
                    suffix="次"
                    :value-style="{ color: '#52c41a', fontSize: '16px' }"
                  />
                </a-col>
              </a-row>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <!-- 30天所有设备汇总表格 -->
      <div class="all-devices-section" style="margin-top: 16px;">
        <a-card size="small" title="30天所有设备数据汇总" class="all-devices-card">
          <template #extra>
            <a-space>
              <a-tag color="blue">设备总数: {{ allDevicesData.length }}</a-tag>
              <a-button size="small" @click="refreshAllDevicesData" :loading="allDevicesLoading">
                刷新
              </a-button>
            </a-space>
          </template>

          <a-table
            :columns="allDevicesColumns"
            :data-source="allDevicesData"
            :loading="allDevicesLoading"
            :pagination="allDevicesPagination"
            :scroll="{ x: 1600, y: 400 }"
            size="small"
            @change="handleAllDevicesTableChange"
            :row-key="(record, index) => index"
          >
            <!-- 设备类型插槽 -->
            <template #deviceType="{ record }">
              <a-tag :color="record.deviceType === '出入口' ? 'green' : 'orange'">
                {{ record.deviceType }}
              </a-tag>
            </template>

            <!-- 数据质量插槽 -->
            <template #qualityRate="{ record }">
              <span :style="{ color: record.qualityRate >= 90 ? '#52c41a' : record.qualityRate >= 80 ? '#faad14' : '#ff4d4f' }">
                {{ record.qualityRate }}%
              </span>
            </template>

            <!-- 状态插槽 -->
            <template #status="{ record }">
              <a-tag :color="record.status === '正常' ? 'green' : record.status === '异常' ? 'red' : 'orange'">
                {{ record.status }}
              </a-tag>
            </template>
          </a-table>
        </a-card>
      </div>

      <!-- 30天详细数据表格 -->
      <div class="month-data-section" style="margin-top: 16px;">
        <!-- 日期选择器 -->
        <div class="date-selector-section" style="margin-bottom: 16px;">
          <a-card size="small">
            <template #title>
              <Icon icon="ant-design:calendar-outlined" style="margin-right: 8px;" />
              选择查看日期
            </template>
            <a-row :gutter="16" align="middle">
              <a-col :span="8">
                <a-select
                  v-model:value="selectedDate"
                  placeholder="请选择日期"
                  style="width: 100%"
                  @change="handleDateChange"
                  :loading="chartsLoading"
                >
                  <a-select-option
                    v-for="date in availableDates"
                    :key="date"
                    :value="date"
                  >
                    {{ formatDateDisplay(date) }}
                  </a-select-option>
                </a-select>
              </a-col>
              <a-col :span="16">
                <a-space>
                  <a-tag v-if="selectedDate" color="blue">
                    已选择：{{ formatDateDisplay(selectedDate) }}
                  </a-tag>
                  <a-tag color="green">
                    出入口：{{ doorDataSource.length }} 条
                  </a-tag>
                  <a-tag color="orange">
                    地磅：{{ poundDataSource.length }} 条
                  </a-tag>
                </a-space>
              </a-col>
            </a-row>
          </a-card>
        </div>

        <a-tabs v-model:activeKey="activeTab" type="card">
          <!-- 出入口数据标签页 -->
          <a-tab-pane key="door" tab="出入口数据">
            <a-card size="small">
              <template #extra>
                <a-space>
                  <span>共 {{ doorPagination.total }} 条数据</span>
                  <a-button size="small" @click="refreshDoorData" :loading="doorLoading">
                    刷新
                  </a-button>
                </a-space>
              </template>

              <a-table
                :columns="doorColumns"
                :data-source="doorDataSource"
                :loading="doorLoading"
                :pagination="doorPagination"
                :scroll="{ x: 1400, y: 300 }"
                size="small"
                @change="handleDoorTableChange"
                :row-key="(record, index) => index"
              >
                <!-- 类型插槽 -->
                <template #type="{ record }">
                  <a-tag :color="record.type === '入厂' ? 'green' : 'blue'">
                    {{ record.type }}
                  </a-tag>
                </template>

                <!-- 识别率插槽 -->
                <template #checkRate="{ record }">
                  <span :style="{ color: record.checkRate >= 90 ? '#52c41a' : record.checkRate >= 80 ? '#faad14' : '#ff4d4f' }">
                    {{ record.checkRate }}%
                  </span>
                </template>
              </a-table>
            </a-card>
          </a-tab-pane>

          <!-- 地磅数据标签页 -->
          <a-tab-pane key="pound" tab="地磅数据">
            <a-card size="small">
              <template #extra>
                <a-space>
                  <span>共 {{ poundPagination.total }} 条数据</span>
                  <a-button size="small" @click="refreshPoundData" :loading="poundLoading">
                    刷新
                  </a-button>
                </a-space>
              </template>

              <a-table
                :columns="poundColumns"
                :data-source="poundDataSource"
                :loading="poundLoading"
                :pagination="poundPagination"
                :scroll="{ x: 1400, y: 300 }"
                size="small"
                @change="handlePoundTableChange"
                :row-key="(record, index) => index"
              >
                <!-- 完整率插槽 -->
                <template #completeRate="{ record }">
                  <span :style="{ color: record.completeRate >= 90 ? '#52c41a' : record.completeRate >= 80 ? '#faad14' : '#ff4d4f' }">
                    {{ record.completeRate }}%
                  </span>
                </template>

                <template #timelyRate="{ record }">
                  <span :style="{ color: record.timelyRate >= 90 ? '#52c41a' : record.timelyRate >= 80 ? '#faad14' : '#ff4d4f' }">
                    {{ record.timelyRate }}%
                  </span>
                </template>

                <!-- 车头车尾统计插槽 -->
                <template #statPoundVos="{ record }">
                  <div v-for="stat in record.statPoundVos" :key="stat.type" style="margin-bottom: 4px;">
                    <a-tag size="small" :color="stat.type === '车头' ? 'blue' : 'orange'">
                      {{ stat.type }}: {{ stat.checkNum }}次 ({{ stat.checkRate }}%)
                    </a-tag>
                  </div>
                </template>
              </a-table>
            </a-card>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, onUnmounted } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { Icon } from '/@/components/Icon';
import { message } from 'ant-design-vue';
import * as echarts from 'echarts';
import {
  generateMockEnterpriseMonthStat,
  getEnterpriseQualityRate30DaysOptimized,
  getEnterpriseDailyDeviceData,
  type StatEnterpriseOfMonthVo,
  type StatEnterpriseOfDoorVo,
  type StatEnterpriseOfPoundVo,
  type StatEnterpriseSummaryVo,
  type StatEnterpriseByDateVo
} from './api';

// 定义组件属性
const props = defineProps({
  title: {
    type: String,
    default: '企业过磅数据统计'
  }
});

// 响应式数据
const chartsLoading = ref(false);
const doorLoading = ref(false);
const poundLoading = ref(false);
const activeTab = ref('door');

// 日期选择相关
const selectedDate = ref<string>('');
const availableDates = ref<string[]>([]);
const selectedDateData = ref<StatEnterpriseByDateVo | null>(null);

// 数据质量指标
const qualityRateData = ref<any>(null);

// 数据源
const doorDataSource = ref<StatEnterpriseOfDoorVo[]>([]);
const poundDataSource = ref<StatEnterpriseOfPoundVo[]>([]);

// 30天所有设备数据
const allDevicesData = ref<any[]>([]);
const allDevicesLoading = ref(false);

// 企业信息
const enterpriseInfo = ref<any>({});

// 企业统计数据
const enterpriseStatData = ref<StatEnterpriseOfMonthVo | null>(null);

// 模态框标题
const modalTitle = computed(() => props.title);

// 统计数据
const statisticsData = reactive({
  totalCount: 0,
  overloadCount: 0,
  avgWeight: 0,
  overloadRate: 0,
});

// 出入口数据分页配置
const doorPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 地磅数据分页配置
const poundPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 30天所有设备数据分页配置
const allDevicesPagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 30天所有设备数据表格列配置
const allDevicesColumns = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    width: 150,
    fixed: 'left',
  },
  {
    title: '设备类型',
    dataIndex: 'deviceType',
    width: 100,
    slots: { customRender: 'deviceType' },
  },
  {
    title: '位置',
    dataIndex: 'location',
    width: 120,
  },
  {
    title: '总过车数',
    dataIndex: 'totalCount',
    width: 100,
  },
  {
    title: '识别数',
    dataIndex: 'recognizedCount',
    width: 100,
  },
  {
    title: '数据质量',
    dataIndex: 'qualityRate',
    width: 100,
    slots: { customRender: 'qualityRate' },
  },
  {
    title: '完整数',
    dataIndex: 'completeCount',
    width: 100,
  },
  {
    title: '及时数',
    dataIndex: 'timelyCount',
    width: 100,
  },
  {
    title: '异常数',
    dataIndex: 'errorCount',
    width: 100,
  },
  {
    title: '设备状态',
    dataIndex: 'status',
    width: 100,
    slots: { customRender: 'status' },
  },
  {
    title: '最后更新时间',
    dataIndex: 'lastUpdateTime',
    width: 160,
  },
];

// 出入口数据表格列配置
const doorColumns = [
  {
    title: '统计日期',
    dataIndex: 'checkTime',
    width: 120,
    customRender: ({ text }: any) => {
      // 只显示日期部分（YYYY-MM-DD）
      return text ? text.split(' ')[0] : '-';
    }
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 80,
    slots: { customRender: 'type' },
  },
  {
    title: '出入口名称',
    dataIndex: 'doorName',
    width: 120,
  },
  {
    title: '过车数',
    dataIndex: 'carNum',
    width: 100,
  },
  {
    title: '识别数',
    dataIndex: 'checkNum',
    width: 100,
  },
  {
    title: '识别率',
    dataIndex: 'checkRate',
    width: 100,
    slots: { customRender: 'checkRate' },
  },
];

// 地磅数据表格列配置
const poundColumns = [
  {
    title: '统计日期',
    dataIndex: 'checkTime',
    width: 120,
    customRender: ({ text }: any) => {
      // 只显示日期部分（YYYY-MM-DD）
      return text ? text.split(' ')[0] : '-';
    }
  },
  {
    title: '地磅名称',
    dataIndex: 'poundName',
    width: 120,
  },
  
  {
    title: '过车数',
    dataIndex: 'carNum',
    width: 100,
  },
  {
      title: '车头/车尾识别数',
      dataIndex: 'statPoundVos',
      width: 200,
      slots: { customRender: 'statPoundVos' },
    },
    {
    title: '完整数',
    dataIndex: 'completeNum',
    width: 100,
  },
  {
    title: '完整率',
    dataIndex: 'completeRate',
    width: 100,
    slots: { customRender: 'completeRate' },
  },
  {
    title: '及时数',
    dataIndex: 'timelyNum',
    width: 100,
  },
 
  {
    title: '及时率',
    dataIndex: 'timelyRate',
    width: 100,
    slots: { customRender: 'timelyRate' },
  },
 
];

// 饼图实例
const timelyRateChart = ref<HTMLElement>();
const completeRateChart = ref<HTMLElement>();
const accurateRateChart = ref<HTMLElement>();

let timelyRateChartInstance: echarts.ECharts | null = null;
let completeRateChartInstance: echarts.ECharts | null = null;
let accurateRateChartInstance: echarts.ECharts | null = null;

// 注册模态框
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  // 保存传入的企业信息
  if (data) {
    enterpriseInfo.value = data;
  }
  
  // 初始化数据
  await initializeData();
});

// 初始化数据
const initializeData = async () => {
  console.log('开始初始化数据...');
  chartsLoading.value = true;
  doorLoading.value = true;
  poundLoading.value = true;

  try {
    // 加载统计数据
    await loadStatisticsData();
    console.log('统计数据加载完成');

    // 加载30天数据
    await loadMonthData();
    console.log('30天数据加载完成');

    // 加载30天所有设备数据
    await loadAllDevicesData();
    console.log('30天所有设备数据加载完成');

    // 等待DOM更新完成
    await nextTick();
    console.log('DOM更新完成，开始初始化饼图...');

    // 延迟一点时间确保DOM完全渲染
    setTimeout(() => {
      initPieCharts();
      console.log('饼图初始化完成');
    }, 100);

  } catch (error) {
    message.error('数据加载失败');
    console.error('数据加载失败:', error);
  } finally {
    chartsLoading.value = false;
    doorLoading.value = false;
    poundLoading.value = false;
  }
};

// 加载统计数据
const loadStatisticsData = async () => {
  try {
    // 获取企业统计数据
    if (enterpriseInfo.value.enterpriseCode) {
      console.log('开始调用两个API接口:', enterpriseInfo.value.enterpriseCode);

      // 分别调用两个接口，互不影响

      // 第一个接口：获取企业30天数据质量率
      try {
        console.log('=== 调用 getEnterpriseQualityRate30DaysOptimized ===');
        const qualityRateResult = await getEnterpriseQualityRate30DaysOptimized(enterpriseInfo.value.enterpriseCode);
        console.log('数据质量率接口返回:', qualityRateResult);

        // 保存数据质量指标（直接使用接口返回的对象）
        qualityRateData.value = qualityRateResult;
        console.log('数据质量指标对象:', qualityRateResult);
        console.log('weighTimelyRate (及时率):', qualityRateResult.weighTimelyRate);
        console.log('weighCompleteRate (完整率):', qualityRateResult.weighCompleteRate);
        console.log('weighAccurateRate (准确率):', qualityRateResult.weighAccurateRate);
      } catch (qualityError) {
        console.warn('数据质量率接口调用失败:', qualityError);
        // 使用模拟的数据质量指标
        qualityRateData.value = {
          weighTimelyRate: 96.77,
          weighCompleteRate: 92.72,
          weighAccurateRate: 94.85
        };
        console.log('使用模拟数据质量指标:', qualityRateData.value);
      }

      // 第二个接口：获取企业每日设备数据（默认传今天日期）
      try {
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
        console.log('=== 调用 getEnterpriseDailyDeviceData ===');
        console.log('传入参数 - 企业编码:', enterpriseInfo.value.enterpriseCode);
        console.log('传入参数 - 查询日期:', today);
        const dailyDeviceResult = await getEnterpriseDailyDeviceData(enterpriseInfo.value.enterpriseCode, today);
        console.log('每日设备数据接口返回:', dailyDeviceResult);

        // 从每日设备数据接口中提取数据（接口返回的是Object）
        console.log('=== 提取每日设备数据 ===');
        console.log('接口返回对象的所有属性:', Object.keys(dailyDeviceResult));
        console.log('doorDeviceData:', dailyDeviceResult.doorDeviceData);
        console.log('poundDeviceData:', dailyDeviceResult.poundDeviceData);

        // 将数据保存到组件变量中
        enterpriseStatData.value = dailyDeviceResult;

        // 提取出入口设备数据 (doorDeviceData)
        if (dailyDeviceResult.doorDeviceData && Array.isArray(dailyDeviceResult.doorDeviceData)) {
          doorDataSource.value = dailyDeviceResult.doorDeviceData;
          doorPagination.total = dailyDeviceResult.doorDeviceData.length;
          console.log('出入口数据 (doorDeviceData):', dailyDeviceResult.doorDeviceData.length, '条');
          console.log('出入口数据第一条:', dailyDeviceResult.doorDeviceData[0]);
        } else {
          doorDataSource.value = [];
          doorPagination.total = 0;
          console.warn('doorDeviceData 不存在或不是数组');
        }

        // 提取地磅设备数据 (poundDeviceData)
        if (dailyDeviceResult.poundDeviceData && Array.isArray(dailyDeviceResult.poundDeviceData)) {
          poundDataSource.value = dailyDeviceResult.poundDeviceData;
          poundPagination.total = dailyDeviceResult.poundDeviceData.length;
          console.log('地磅数据 (poundDeviceData):', dailyDeviceResult.poundDeviceData.length, '条');
          console.log('地磅数据第一条:', dailyDeviceResult.poundDeviceData[0]);
        } else {
          poundDataSource.value = [];
          poundPagination.total = 0;
          console.warn('poundDeviceData 不存在或不是数组');
        }

      } catch (deviceError) {
        console.warn('每日设备数据接口调用失败:', deviceError);
        // 使用模拟数据
        enterpriseStatData.value = generateMockEnterpriseMonthStat(
          enterpriseInfo.value.enterpriseCode || 'TEST001',
          enterpriseInfo.value.enterpriseName || '测试企业'
        );

        // 设置空的表格数据
        doorDataSource.value = [];
        doorPagination.total = 0;
        poundDataSource.value = [];
        poundPagination.total = 0;

        console.log('使用模拟数据，表格数据已清空');
      }

      // 设置基础统计数据（简化处理，主要数据来源于质量指标接口）
      statisticsData.totalCount = 1234; // 总过车次数
      statisticsData.overloadCount = 1156; // 识别次数
      statisticsData.avgWeight = 28.5; // 平均重量
      statisticsData.overloadRate = 93.7; // 识别率

      console.log('基础统计数据设置完成:', statisticsData);
      console.log('数据质量指标将从 getEnterpriseQualityRate30DaysOptimized 接口获取');

      console.log('企业统计数据加载完成:', enterpriseStatData.value);
    }
  } catch (error) {
    console.error('加载企业统计数据失败:', error);
    // 降级到模拟数据
    enterpriseStatData.value = generateMockEnterpriseMonthStat(
      'FALLBACK001',
      '降级测试企业'
    );
  }
};

// 删除了重复的handleDateChange函数

// 格式化日期显示
const formatDateDisplay = (dateStr: string) => {
  const date = new Date(dateStr);
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  if (dateStr === today.toISOString().split('T')[0]) {
    return `${dateStr} (今天)`;
  } else if (dateStr === yesterday.toISOString().split('T')[0]) {
    return `${dateStr} (昨天)`;
  } else {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const weekday = weekdays[date.getDay()];
    return `${dateStr} (${weekday})`;
  }
};

// 初始化日期选择器（生成近30天的日期）
const initializeDateSelector = () => {
  console.log('=== 初始化日期选择器（近30天） ===');

  // 生成近30天的日期数组
  const dates: string[] = [];
  const today = new Date();

  for (let i = 0; i < 30; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD格式
    dates.push(dateStr);
  }

  // 按日期倒序排列（最新的在前面）
  availableDates.value = dates.sort((a, b) => new Date(b).getTime() - new Date(a).getTime());

  // 默认选择今天
  selectedDate.value = availableDates.value[0]; // 今天的日期

  console.log('生成的近30天日期:', availableDates.value);
  console.log('默认选择日期（今天）:', selectedDate.value);
};

// 处理日期选择变化
const handleDateChange = async (date: string) => {
  console.log('=== 日期选择变化 ===');
  console.log('选择的日期:', date);

  if (!date || !enterpriseInfo.value.enterpriseCode) {
    console.warn('日期或企业编码为空，无法加载数据');
    return;
  }

  // 设置加载状态
  doorLoading.value = true;
  poundLoading.value = true;

  try {
    console.log('=== 根据选择日期调用 getEnterpriseDailyDeviceData ===');
    console.log('传入参数 - 企业编码:', enterpriseInfo.value.enterpriseCode);
    console.log('传入参数 - 查询日期:', date);

    // 调用接口获取选中日期的数据
    const dailyDeviceResult = await getEnterpriseDailyDeviceData(enterpriseInfo.value.enterpriseCode, date);
    console.log('选中日期的设备数据接口返回:', dailyDeviceResult);

    // 提取并更新数据
    console.log('=== 更新选中日期的设备数据 ===');
    console.log('接口返回对象的所有属性:', Object.keys(dailyDeviceResult));

    // 提取出入口设备数据 (doorDeviceData)
    if (dailyDeviceResult.doorDeviceData && Array.isArray(dailyDeviceResult.doorDeviceData)) {
      doorDataSource.value = dailyDeviceResult.doorDeviceData;
      doorPagination.total = dailyDeviceResult.doorDeviceData.length;
      doorPagination.current = 1; // 重置到第一页
      console.log(`${date} 出入口数据 (doorDeviceData):`, dailyDeviceResult.doorDeviceData.length, '条');
      if (dailyDeviceResult.doorDeviceData.length > 0) {
        console.log('出入口数据第一条:', dailyDeviceResult.doorDeviceData[0]);
      }
    } else {
      doorDataSource.value = [];
      doorPagination.total = 0;
      console.warn(`${date} doorDeviceData 不存在或不是数组`);
    }

    // 提取地磅设备数据 (poundDeviceData)
    if (dailyDeviceResult.poundDeviceData && Array.isArray(dailyDeviceResult.poundDeviceData)) {
      poundDataSource.value = dailyDeviceResult.poundDeviceData;
      poundPagination.total = dailyDeviceResult.poundDeviceData.length;
      poundPagination.current = 1; // 重置到第一页
      console.log(`${date} 地磅数据 (poundDeviceData):`, dailyDeviceResult.poundDeviceData.length, '条');
      if (dailyDeviceResult.poundDeviceData.length > 0) {
        console.log('地磅数据第一条:', dailyDeviceResult.poundDeviceData[0]);
      }
    } else {
      poundDataSource.value = [];
      poundPagination.total = 0;
      console.warn(`${date} poundDeviceData 不存在或不是数组`);
    }

    console.log(`=== ${date} 数据加载完成 ===`);
    console.log('出入口数据条数:', doorDataSource.value.length);
    console.log('地磅数据条数:', poundDataSource.value.length);

  } catch (error) {
    console.error(`加载 ${date} 数据失败:`, error);
    message.error(`加载 ${date} 数据失败`);

    // 出错时清空数据
    doorDataSource.value = [];
    doorPagination.total = 0;
    poundDataSource.value = [];
    poundPagination.total = 0;
  } finally {
    // 取消加载状态
    doorLoading.value = false;
    poundLoading.value = false;
  }
};

// 加载数据（现在数据已经在API调用时直接赋值了）
const loadMonthData = async () => {
  console.log('=== 数据加载完成 ===');
  console.log('出入口数据来源: doorDeviceData');
  console.log('地磅数据来源: poundDeviceData');
  console.log('出入口数据条数:', doorDataSource.value.length);
  console.log('地磅数据条数:', poundDataSource.value.length);

  // 初始化日期选择器
  initializeDateSelector();
};

// 加载30天所有设备数据
const loadAllDevicesData = async () => {
  console.log('=== 加载30天所有设备数据 ===');

  try {
    // 生成模拟的30天所有设备数据
    const mockData = generateMock30DaysAllDevicesData();
    allDevicesData.value = mockData;
    allDevicesPagination.total = mockData.length;

    console.log('30天所有设备数据加载完成，共', mockData.length, '条');
  } catch (error) {
    console.error('加载30天所有设备数据失败:', error);
    allDevicesData.value = [];
    allDevicesPagination.total = 0;
  }
};

// 初始化所有饼图
const initPieCharts = () => {
  console.log('开始初始化饼图...');
  initTimelyRateChart();
  initCompleteRateChart();
  initAccurateRateChart();
};

// 初始化及时率饼图
const initTimelyRateChart = () => {
  if (!timelyRateChart.value) return;

  timelyRateChartInstance = echarts.init(timelyRateChart.value);

  const rate = qualityRateData.value?.weighTimelyRate || 0;
  const total = qualityRateData.value?.totalCarCount || 0;
  const count = Math.round((rate / 100) * total); // 根据比率和总数计算及时数

  const option = createPieChartOption('及时率', rate, '#52c41a', count, total);

  timelyRateChartInstance.setOption(option);
  console.log('及时率饼图初始化完成，数值:', rate, '及时数:', count, '总数:', total);
};

// 初始化完整率饼图
const initCompleteRateChart = () => {
  if (!completeRateChart.value) return;

  completeRateChartInstance = echarts.init(completeRateChart.value);

  const rate = qualityRateData.value?.weighCompleteRate || 0;
  const total = qualityRateData.value?.totalCarCount || 0;
  const count = Math.round((rate / 100) * total); // 根据比率和总数计算完整数

  const option = createPieChartOption('完整率', rate, '#faad14', count, total);

  completeRateChartInstance.setOption(option);
  console.log('完整率饼图初始化完成，数值:', rate, '完整数:', count, '总数:', total);
};

// 初始化准确率饼图
const initAccurateRateChart = () => {
  if (!accurateRateChart.value) return;

  accurateRateChartInstance = echarts.init(accurateRateChart.value);

  const rate = qualityRateData.value?.weighAccurateRate || 0;
  const total = qualityRateData.value?.totalCarCount || 0;
  const count = Math.round((rate / 100) * total); // 根据比率和总数计算准确数

  const option = createPieChartOption('准确率', rate, '#722ed1', count, total);

  accurateRateChartInstance.setOption(option);
  console.log('准确率饼图初始化完成，数值:', rate, '准确数:', count, '总数:', total);
};

// 创建饼图配置（增强版，显示详细信息）
const createPieChartOption = (name: string, rate: number, color: string, count: number, total: number) => {
  const remainingRate = 100 - rate;

  return {
    tooltip: {
      trigger: 'item',
      formatter: function(params: any) {
        if (params.name === name) {
          return `${name}<br/>数量: ${count}<br/>总数: ${total}<br/>比率: ${rate.toFixed(2)}%`;
        } else {
          return `未达标: ${remainingRate.toFixed(2)}%`;
        }
      }
    },
    series: [
      {
        name: name,
        type: 'pie',
        radius: ['45%', '75%'],
        center: ['50%', '55%'],
        startAngle: 90,
        data: [
          {
            value: rate,
            name: name,
            itemStyle: {
              color: color
            },
            label: {
              show: true,
              position: 'center',
              formatter: function() {
                return `{a|${rate.toFixed(2)}%}\n{b|${name}}\n{c|${count}/${total}}`;
              },
              rich: {
                a: {
                  fontSize: 20,
                  fontWeight: 'bold',
                  color: color,
                  lineHeight: 24
                },
                b: {
                  fontSize: 12,
                  color: '#666',
                  padding: [4, 0, 0, 0],
                  lineHeight: 14
                },
                c: {
                  fontSize: 11,
                  color: '#999',
                  padding: [2, 0, 0, 0],
                  lineHeight: 12
                }
              }
            },
            labelLine: {
              show: false
            }
          },
          {
            value: remainingRate,
            name: '未达标',
            itemStyle: {
              color: '#f0f0f0'
            },
            label: {
              show: false
            },
            labelLine: {
              show: false
            }
          }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
};

// 格式化重量显示
const formatWeight = (weight: number) => {
  if (!weight) return '-';
  return `${(weight / 1000).toFixed(2)}吨`;
};

// 根据质量指标值获取颜色
const getQualityColor = (value: number) => {
  if (value >= 95) return '#52c41a'; // 绿色 - 优秀
  if (value >= 90) return '#1890ff'; // 蓝色 - 良好
  if (value >= 80) return '#faad14'; // 黄色 - 一般
  if (value >= 70) return '#fa8c16'; // 橙色 - 较差
  return '#ff4d4f'; // 红色 - 差
};

// 出入口表格变化处理
// 出入口表格变化处理
const handleDoorTableChange = (pag: any) => {
  doorPagination.current = pag.current;
  doorPagination.pageSize = pag.pageSize;
  console.log('出入口表格分页变化:', pag);
  console.log('当前出入口数据总数:', doorPagination.total);
  // 注意：Ant Design Vue 的表格组件会自动处理分页显示，这里只需要更新分页状态
};

// 地磅表格变化处理
const handlePoundTableChange = (pag: any) => {
  poundPagination.current = pag.current;
  poundPagination.pageSize = pag.pageSize;
  console.log('地磅表格分页变化:', pag);
  console.log('当前地磅数据总数:', poundPagination.total);
  // 注意：Ant Design Vue 的表格组件会自动处理分页显示，这里只需要更新分页状态
};

// 30天所有设备表格变化处理
const handleAllDevicesTableChange = (pag: any) => {
  allDevicesPagination.current = pag.current;
  allDevicesPagination.pageSize = pag.pageSize;
  console.log('30天所有设备表格分页变化:', pag);
  console.log('当前30天所有设备数据总数:', allDevicesPagination.total);
};

// 删除了导出数据函数

// 刷新出入口数据
const refreshDoorData = async () => {
  console.log('=== 刷新出入口数据 ===');
  console.log('当前选中日期:', selectedDate.value);

  if (!selectedDate.value || !enterpriseInfo.value.enterpriseCode) {
    message.error('缺少必要参数，无法刷新');
    return;
  }

  doorLoading.value = true;
  try {
    console.log('调用接口刷新出入口数据');
    console.log('传入参数 - 企业编码:', enterpriseInfo.value.enterpriseCode);
    console.log('传入参数 - 查询日期:', selectedDate.value);

    // 调用接口获取当前选中日期的数据
    const dailyDeviceResult = await getEnterpriseDailyDeviceData(enterpriseInfo.value.enterpriseCode, selectedDate.value);
    console.log('刷新接口返回:', dailyDeviceResult);

    // 只更新出入口数据
    if (dailyDeviceResult.doorDeviceData && Array.isArray(dailyDeviceResult.doorDeviceData)) {
      doorDataSource.value = dailyDeviceResult.doorDeviceData;
      doorPagination.total = dailyDeviceResult.doorDeviceData.length;
      doorPagination.current = 1; // 重置到第一页
      console.log('出入口数据刷新成功，共', dailyDeviceResult.doorDeviceData.length, '条');
      message.success('出入口数据刷新成功');
    } else {
      doorDataSource.value = [];
      doorPagination.total = 0;
      console.warn('刷新返回的出入口数据为空');
      message.warning('出入口数据为空');
    }
  } catch (error) {
    console.error('刷新出入口数据失败:', error);
    message.error('刷新出入口数据失败');
  } finally {
    doorLoading.value = false;
  }
};

// 刷新地磅数据
const refreshPoundData = async () => {
  console.log('=== 刷新地磅数据 ===');
  console.log('当前选中日期:', selectedDate.value);

  if (!selectedDate.value || !enterpriseInfo.value.enterpriseCode) {
    message.error('缺少必要参数，无法刷新');
    return;
  }

  poundLoading.value = true;
  try {
    console.log('调用接口刷新地磅数据');
    console.log('传入参数 - 企业编码:', enterpriseInfo.value.enterpriseCode);
    console.log('传入参数 - 查询日期:', selectedDate.value);

    // 调用接口获取当前选中日期的数据
    const dailyDeviceResult = await getEnterpriseDailyDeviceData(enterpriseInfo.value.enterpriseCode, selectedDate.value);
    console.log('刷新接口返回:', dailyDeviceResult);

    // 只更新地磅数据
    if (dailyDeviceResult.poundDeviceData && Array.isArray(dailyDeviceResult.poundDeviceData)) {
      poundDataSource.value = dailyDeviceResult.poundDeviceData;
      poundPagination.total = dailyDeviceResult.poundDeviceData.length;
      poundPagination.current = 1; // 重置到第一页
      console.log('地磅数据刷新成功，共', dailyDeviceResult.poundDeviceData.length, '条');
      message.success('地磅数据刷新成功');
    } else {
      poundDataSource.value = [];
      poundPagination.total = 0;
      console.warn('刷新返回的地磅数据为空');
      message.warning('地磅数据为空');
    }
  } catch (error) {
    console.error('刷新地磅数据失败:', error);
    message.error('刷新地磅数据失败');
  } finally {
    poundLoading.value = false;
  }
};

// 刷新30天所有设备数据
const refreshAllDevicesData = async () => {
  console.log('=== 刷新30天所有设备数据 ===');

  if (!enterpriseInfo.value.enterpriseCode) {
    message.error('缺少企业编码，无法刷新');
    return;
  }

  allDevicesLoading.value = true;
  try {
    // 生成模拟的30天所有设备数据
    const mockData = generateMock30DaysAllDevicesData();
    allDevicesData.value = mockData;
    allDevicesPagination.total = mockData.length;
    allDevicesPagination.current = 1;

    console.log('30天所有设备数据刷新成功，共', mockData.length, '条');
    message.success('30天所有设备数据刷新成功');
  } catch (error) {
    console.error('刷新30天所有设备数据失败:', error);
    message.error('刷新30天所有设备数据失败');
  } finally {
    allDevicesLoading.value = false;
  }
};

// 生成模拟的30天所有设备数据
const generateMock30DaysAllDevicesData = () => {
  const mockData: any[] = [];

  // 出入口设备
  for (let i = 1; i <= 8; i++) {
    const totalCount = Math.floor(Math.random() * 1000) + 500;
    const recognizedCount = Math.floor(totalCount * (0.85 + Math.random() * 0.1));
    const completeCount = Math.floor(recognizedCount * (0.9 + Math.random() * 0.08));
    const timelyCount = Math.floor(recognizedCount * (0.92 + Math.random() * 0.06));
    const errorCount = Math.floor(Math.random() * 20);
    const qualityRate = Math.floor((recognizedCount / totalCount) * 100);

    mockData.push({
      deviceName: `出入口设备${i}`,
      deviceType: '出入口',
      location: `${i}号门`,
      totalCount,
      recognizedCount,
      qualityRate,
      completeCount,
      timelyCount,
      errorCount,
      status: Math.random() > 0.1 ? '正常' : (Math.random() > 0.5 ? '异常' : '维护'),
      lastUpdateTime: new Date(Date.now() - Math.floor(Math.random() * 24 * 60 * 60 * 1000)).toLocaleString(),
    });
  }

  // 地磅设备
  for (let i = 1; i <= 6; i++) {
    const totalCount = Math.floor(Math.random() * 800) + 300;
    const recognizedCount = Math.floor(totalCount * (0.88 + Math.random() * 0.08));
    const completeCount = Math.floor(recognizedCount * (0.85 + Math.random() * 0.1));
    const timelyCount = Math.floor(recognizedCount * (0.9 + Math.random() * 0.08));
    const errorCount = Math.floor(Math.random() * 15);
    const qualityRate = Math.floor((recognizedCount / totalCount) * 100);

    mockData.push({
      deviceName: `地磅设备${i}`,
      deviceType: '地磅',
      location: `${i}号地磅`,
      totalCount,
      recognizedCount,
      qualityRate,
      completeCount,
      timelyCount,
      errorCount,
      status: Math.random() > 0.15 ? '正常' : (Math.random() > 0.5 ? '异常' : '维护'),
      lastUpdateTime: new Date(Date.now() - Math.floor(Math.random() * 24 * 60 * 60 * 1000)).toLocaleString(),
    });
  }

  return mockData;
};

// 确定按钮处理
const handleOk = () => {
  closeModal();
};

// 取消按钮处理
const handleCancel = () => {
  closeModal();
};

// 组件卸载时销毁饼图实例
onUnmounted(() => {
  if (timelyRateChartInstance) {
    timelyRateChartInstance.dispose();
  }
  if (completeRateChartInstance) {
    completeRateChartInstance.dispose();
  }
  if (accurateRateChartInstance) {
    accurateRateChartInstance.dispose();
  }
});
</script>

<style lang="less" scoped>
.enterprise-weight-modal {
  .enterprise-header {
    margin-bottom: 16px;
  }

  .quality-section {
    margin-bottom: 16px;
  }

  .all-devices-section {
    margin-bottom: 16px;

    .all-devices-card {
      .ant-card-body {
        padding: 12px;
      }
    }
  }

  .month-data-section {
    .ant-card-body {
      padding: 12px;
    }
  }

  .quality-metrics-card {
    .pie-chart-container {
      text-align: center;
      padding: 8px;

      .chart-title {
        font-size: 14px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        font-weight: 500;
        height: 24px;
      }

      .pie-chart {
        height: 240px;
        width: 100%;
      }
    }

    .ant-statistic {
      .ant-statistic-title {
        font-size: 12px;
        color: #666;
      }

      .ant-statistic-content {
        .ant-statistic-content-value {
          font-size: 16px;
        }
      }
    }
  }
}
</style>
