<template>
  <div class="test-charts-container">
    <a-card title="图表测试页面">
      <a-space direction="vertical" style="width: 100%;">
        <a-button type="primary" @click="openModal">
          测试企业过磅数据统计
        </a-button>
        
        <a-alert 
          message="请打开浏览器控制台查看调试信息" 
          type="info" 
          show-icon 
        />
      </a-space>
    </a-card>

    <!-- 企业过磅数据统计弹框 -->
    <EnterpriseWeightModal @register="registerModal" />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal';
import EnterpriseWeightModal from './EnterpriseWeightModal.vue';

// 注册弹框
const [registerModal, { openModal: openModalFn }] = useModal();

// 测试函数
const openModal = () => {
  console.log('打开企业过磅数据统计弹框...');
  openModalFn(true, {
    enterpriseId: 'test_001',
    enterpriseName: '山西煤炭集团测试企业',
    enterpriseCode: 'TEST001',
  });
};
</script>

<style lang="less" scoped>
.test-charts-container {
  padding: 20px;
}
</style>
