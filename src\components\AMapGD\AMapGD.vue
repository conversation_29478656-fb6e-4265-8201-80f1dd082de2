<!-- src/components/CustomMap.vue -->
<template>
  <div>
    <div style="display: flex">
      <a-form-item name="">
        <Input 
          style="width: 138px" 
          :value="selectedPosition" 
          @change="onChange" 
        />
      </a-form-item>
      <Button @click="openModal">选择位置</Button>
    </div>
    <Modal v-model:open="open" title="选择位置" @ok="handleOk" @cancel="cancel">
      <input type="text" id="keyword" name="keyword" />
      <div id="customMap" style="width: 100%; height: 500px"></div>
    </Modal>
  </div>
</template>

<script lang="ts">
import { ref, watch, defineComponent } from 'vue';
import { load } from '@amap/amap-jsapi-loader';
import Modal from '../Modal/src/components/Modal';
import { Input, Button, Form } from 'ant-design-vue';
import { useCounterStore } from '@/store/modules/getMapData';

export default defineComponent({
  name: 'CustomMap',
  components: {
    Modal,
    Input,
    Button,
    Form,
  },
  emits: ['update:selectedPositionProps'],
  props: {
    selectedPositionProps: { type: String, default: '' },
  },
  setup(props, { emit, expose }) {
    const open = ref<boolean>(false);
    const counterStore = useCounterStore();

    const markers = ref<any[]>([[112.852022, 35.491315]]);
    const map = ref<AMap.Map | null>(null);
    const markerList = ref<AMap.Marker[]>([]);
    const selectedPosition = ref<string>(counterStore.mapData || '112.852022,35.491315');
    const searchValue = ref<string>('');
    const select = ref<string>('');

    // 监听 store 中的 mapData 变化
    watch(
      () => counterStore.mapData,
      (newMapData) => {
        selectedPosition.value = newMapData;
      },
      { immediate: true }
    );

    const handleOk = () => {
      open.value = false;
      selectedPosition.value = select.value || '112.852022,35.491315';
      console.log(selectedPosition.value, select.value, '===>selectedPosition.value');
      counterStore.setCount(selectedPosition.value);
      emit('update:selectedPositionProps', selectedPosition.value);
      const keywordInput = document.getElementById('keyword') as HTMLInputElement;
  if (keywordInput) {
    keywordInput.value = '';
  }

    };
    const cancel = () => {
      open.value = false;
      const keywordInput = document.getElementById('keyword') as HTMLInputElement;
      if (keywordInput) {
        keywordInput.value = '';
      }
    };
    const openModal = () => {
      open.value = true;
      initMap();
    };

    const onChange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      console.log(target.value, '====>e.target.value)');
      counterStore.setCount(target.value || '112.852022,35.491315');
      emit('update:selectedPositionProps', target.value);
    };

    const initMap = async () => {
      try {
        await load({
          key: '3ba95f602bc77ee6d74a6e1a54b893d9', // 替换为您的高德地图API密钥
          version: '1.4.15',
          plugins: ['AMap.Autocomplete', 'AMap.PlaceSearch'],
        });

        map.value = new window.AMap.Map('customMap', {
          center: [112.852022, 35.491315],
          zoom: 13,
          zooms: [11, 16], // 设置最小和最大缩放级别
        });
        // 初始化标记
        markers.value.forEach((position) => {
          const marker = new window.AMap.Marker({
            position,
          });
          marker.setMap(map.value);
          markerList.value.push(marker);
        });

        // 添加地图点击事件
        map.value.on('click', handleMapClick);

        // 初始化 Autocomplete 和 PlaceSearch 插件
        new window.AMap.plugin(['AMap.Autocomplete', 'AMap.PlaceSearch'], function () {
          const autoOptions = {
            city: '晋城', // 城市，默认全国
            input: 'keyword', // 使用联想输入的 input 的 id
          };
          const autocomplete = new window.AMap.Autocomplete(autoOptions);
          console.log('Autocomplete initialized:', autocomplete);

          const placeSearch = new window.AMap.PlaceSearch({
            city: '晋城',
            map: map.value,
            citylimit: true,
          });
          console.log('PlaceSearch initialized:', placeSearch);

          // 监听选择事件
          autocomplete.on('select', function (e: any) {
            console.log('Autocomplete select event:', e);

            // 清除之前的标记
            markerList.value.forEach((marker) => {
              marker.setMap(null);
            });
            markerList.value = [];

            // 获取选中的位置
            const position = e.poi.location 
              ? [e.poi.location.lng, e.poi.location.lat] 
              : null;

            if (!position) {
              return;
            }

            // 添加新的标记
            const marker = new window.AMap.Marker({
              position,
            });
            marker.setMap(map.value);
            markerList.value.push(marker);

            select.value = `${e.poi.location.lng},${e.poi.location.lat}`;
            markers.value = [[e.poi.location.lng, e.poi.location.lat]];

            // 跳转到新的位置
            map.value.setCenter(position);
            map.value.setZoom(15); // 根据需要调整缩放级别
          });
        });
      } catch (error) {
        console.error('初始化地图失败:', error);
      }
    };

    const handleMapClick = (e: any) => {
      const { lnglat } = e;
      const [lng, lat] = [lnglat.getLng(), lnglat.getLat()];
      select.value = `${lng},${lat}`;
      markers.value = [[lng, lat]];
    };

    const updateMarkers = () => {
      // 清除所有标记
      markerList.value.forEach((marker) => marker.setMap(null));
      markerList.value = [];

      // 添加新的标记
      markers.value.forEach((position) => {
        const marker = new window.AMap.Marker({
          position,
        });
        marker.setMap(map.value);
        markerList.value.push(marker);
      });

      // 中心跳转
      if (markers.value.length > 0) {
        map.value.setCenter(markers.value[0]);
      }
    };

    // 监听 markers 的变化，更新标记
    watch(
      () => markers.value,
      () => {
        if (map.value) {
          updateMarkers();
        }
      },
      { deep: true }
    );

    expose({
      openModal,
    });

    return {
      handleOk,
      openModal,
      open,
      searchValue,
      selectedPosition,
      onChange,
      cancel
    };
  },
});
</script>

<style scoped>
#customMap {
  width: 100%;
  height: 500px;
}
#keyword {
  position: absolute;
    top: 68px;
    left: 19px;
    z-index: 9999;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.title:hover {
  cursor: pointer;
  color: #1890ff;
  font-weight: 600;
}

#my-panel {
  width: 300px;
  height: 300px;
}
</style>
