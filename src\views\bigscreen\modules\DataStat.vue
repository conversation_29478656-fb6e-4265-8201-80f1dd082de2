<template>
  <div class="container">
    <div :key="updateKey" class="container_box" style="background: #041451">
      <div class="header">
        <div class="title">超限检测站称重数据统计</div>
        <div class="title">超限检测站称重数据统计</div>
        <div class="chose_time">
          <a-space direction="vertical" :size="12" />
          <div class="button_group">
            <a-select :value="data.type" @change="handleChange">
              <a-select-option value="day">当天</a-select-option>
              <a-select-option value="month">当月</a-select-option>
              <a-select-option value="year">当年</a-select-option>
            </a-select>
          </div>
          <div class="button_select">
            <template v-if="data.type === 'day'">
              <a-date-picker :format="data.dateFormat" v-model:value="data.date" @change="timeChange" allowClear />
            </template>
            <template v-else-if="data.type == 'month'">
              <a-month-picker placeholder="选择月份" v-model:value="data.date" @change="timeChange" allowClear />
            </template>
            <template v-else-if="data.type == 'year'">
              <a-date-picker
                placeholder="选择年份"
                v-decorator="['year']"
                mode="year"
                format="YYYY"
                :open="data.open"
                valueFormat="YYYY"
                style="width: 100%"
                v-model:value="data.date"
                allowClear
                @change="timeChange"
                @open-change="openChangeOne"
                @panel-change="panelChangeOne"
              />
            </template>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="left">
          <div class="data">
            <div class="info title" style="color: white">动态磅数据</div>
            <div class="info num">
              <CountFlop :val="data.dy_val" />
              <div class="info" style="display: inline-block; vertical-align: top; color: white">辆</div>
            </div>
          </div>
          <div class="data">
            <div class="info title" style="color: white">静态磅数据</div>
            <div class="info num">
              <CountFlop :val="data.static_val" />
              <div class="info" style="display: inline-block; vertical-align: top; color: white">辆</div>
            </div>
          </div>
          <div class="data">
            <div class="info title" style="color: white">复检数据</div>
            <div class="info num">
              <CountFlop :val="data.copy_val" />
              <div class="info" style="display: inline-block; vertical-align: top; color: white">辆</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import CountFlop from '@/views/bigscreen/components/CountFlop/index.vue';
  // import moment from 'moment';
  import { onMounted, ref, watch } from 'vue';
  import dayjs from 'dayjs';
  import { getData } from '@/views/bigscreen/bigScreen.api';
  const data = ref({
    title: '超限率',
    open: false,
    type: 'day',
    dateFormat: 'YYYY-MM-DD',
    monthFormat: 'YYYY-MM',
    date: dayjs(),
    dy_val: 0,
    static_val: 0,
    copy_val: 0,
    over_rate: 0,
  });
  onMounted(() => {
    updateKey.value = 1;
  });
  const updateKey = ref(0);
  watch(updateKey, (newKey, oldKey) => {
    initData(data.value.date);
  });

  const handleChange = (value) => {
    data.value.type = value;
    initData(data.value.date);
  };
  const timeChange = (value) => {
    initData(value);
  };
  const initData = (time) => {
    let datetime = time.format('YYYY-MM-DD');
    getData({ type: data.value.type, time: datetime }).then((res) => {
      data.value.dy_val = res.result.dynamicDataCount;
      data.value.static_val = res.result.staticDataCount;
      data.value.copy_val = res.result.copyDataCount;
      data.value.over_rate = parseInt(res.result.overRate);
    });
  };
  // 弹出日历和关闭日历的回调
  const openChangeOne = (status) => {
    if (status) {
      data.value.open = true;
    }
  };
  // 得到年份选择器的值
  const panelChangeOne = (value) => {
    data.value.open = false;
    data.value.date = value;
    initData(dayjs(value));
  };
</script>
<style lang="less" scoped>
  .container {
    position: relative;
    .container_box {
      .header {
        display: flex;
        flex: 1;
        flex-direction: row;
        justify-content: space-between;
        margin: 10px 10px 5px 15px;

        .title {
          font-size: 0.8125rem /* 13/16 */;
          flex: 1.2;
          font-weight: bolder;
          color: white;
        }

        .chose_time {
          width: 120px;
          display: flex;
          flex: 1;
          flex-direction: row;
          justify-content: end;

          .button_group {
            padding: 0px 10px;
          }
        }
      }

      .content {
        height: 9rem /* 145/16 */;
        display: flex;
        flex-direction: row;
        padding: 15px;

        .left {
          flex: 2;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .data {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            height: 30px;
            line-height: 30px;

            .info {
              margin-left: 5px;
              flex: 1;
            }

            .title {
              width: 69px;
              font-size: 0.9325rem;
              flex: 5;
            }

            .num {
              flex: 10;
            }
          }
        }

        .right {
          text-align: center;
          flex: 1;
        }
      }
    }
  }
  .info title {
    color: white;
  }

  .myself {
    height: 32px;

    :deep(.count-flop) {
      height: 2rem /* 32/16 */;
      line-height: 32px;
      font-size: 36px;
      color: red;
    }

    :deep(.count-flop-box) {
      margin-right: 0;
      width: 22px;
      border: 0;
      border-radius: 0;
      line-height: 32px;
    }

    :deep(.count-flop-point) {
      margin-right: 0;
    }

    :deep(.count-flop-unit) {
      font-size: 1.5625rem /* 25/16 */;
    }
  }
  .container {
    :deep(.ant-select-selector) {
      background: #1769ff;
      height: 20px;
      font-size: 12px;
    }
    :deep(.ant-select-selection-item) {
      color: #ffffff;
    }
  }
  .button_group {
    :deep(.ant-select-selection-item) {
      color: white !important;
      font-size: 12px;
      line-height: 20px;
    }
  }
  .container {
    :deep(.ant-picker) {
      height: 20px;
      background: #1769ff;
      color: #ffffff;
    }
    :deep(.ant-picker-input > input) {
      font-size: 12px;
      color: #ffffff;
      &::-webkit-input-placeholder {
        color: white;
        font-size: 12px;
      }
    }
    :deep(.ant-picker-input) {
      font-size: 12px;
      color: #ffffff;
    }
  }
  :deep(.ant-select-selection) {
    border: none;
    border-radius: 0;
    background: #1769ff;
    color: white;
  }

  :deep(.ant-select-selection--single) {
    height: 20px;
  }

  .container {
    :deep(.ant-select-selection__rendered) {
      margin-left: 5px;
      line-height: 20px;
      color: #ffffff;
    }
  }

  :deep(.ant-select-selection-selected-value) {
    -webkit-transform: scale(0.8);
  }

  .container {
    :deep(.ant-select-arrow) {
      color: white;
    }
  }

  :deep(.ant-select-dropdown-menu-item) {
    font-size: 12px;
    line-height: 16px;
  }

  :deep(.ant-calendar-picker) {
    font-size: 12px;
    color: white;
  }
  .container {
    :deep(.ant-input) {
      background-color: #1769ff;
      border: 0 solid #d9d9d9;
      border-radius: 0;
      height: 20px;
      color: #fff;
      &::-webkit-input-placeholder {
        color: white;
        font-size: 12px;
      }
    }
  }

  :deep(.ant-input:hover) {
    border: none;
  }
  .container {
    :deep(.anticon-calendar) {
      color: #ffffff !important;
    }
  }
</style>
