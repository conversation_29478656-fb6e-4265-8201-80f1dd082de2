<template>
  <div class="demo-container">
    <a-card title="过磅数据弹框组件示例">
      <a-space>
        <a-button type="primary" @click="openModal">
          打开过磅数据弹框
        </a-button>
        <a-button @click="openModalWithTitle">
          自定义标题打开
        </a-button>
      </a-space>
    </a-card>

    <!-- 过磅数据弹框 -->
    <WeightDataModal @register="registerModal" />
    
    <!-- 自定义标题的弹框 -->
    <WeightDataModal 
      @register="registerCustomModal" 
      title="企业过磅数据查询"
    />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal';
import WeightDataModal from './index.vue';

// 注册默认弹框
const [registerModal, { openModal: openDefaultModal }] = useModal();

// 注册自定义标题弹框
const [registerCustomModal, { openModal: openCustomModalFn }] = useModal();

// 打开默认弹框
const openModal = () => {
  openDefaultModal(true, {
    // 可以传递初始化数据
  });
};

// 打开自定义标题弹框
const openModalWithTitle = () => {
  openCustomModalFn(true, {
    // 可以传递初始化数据
  });
};
</script>

<style lang="less" scoped>
.demo-container {
  padding: 20px;
}
</style>
