<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="modalTitle"
    :width="1200"
    :min-height="600"
    :can-fullscreen="true"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="weight-data-modal">
      <!-- 企业信息和时间选择区域 -->
      <div class="header-section">
        <a-card size="small">
          <!-- 企业信息显示 -->
          <div v-if="enterpriseInfo.enterpriseName" class="enterprise-info">
            <a-descriptions :column="3" size="small" bordered>
              <a-descriptions-item label="企业名称">{{ enterpriseInfo.enterpriseName }}</a-descriptions-item>
              <a-descriptions-item label="企业编码">{{ enterpriseInfo.enterpriseCode || '-' }}</a-descriptions-item>
              <a-descriptions-item label="查询时间范围">
                {{ dateRange ? `${dateRange[0].format('YYYY-MM-DD')} 至 ${dateRange[1].format('YYYY-MM-DD')}` : '-' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>

          <div style="margin-top: 16px;">
            <a-form layout="inline">
              <a-form-item label="时间范围（最多30天）">
                <a-range-picker
                  v-model:value="dateRange"
                  :format="dateFormat"
                  :placeholder="['开始日期', '结束日期']"
                  :disabled-date="disabledDate"
                  @change="handleDateChange"
                  @calendar-change="onCalendarChange"
                  style="width: 300px"
                />
              </a-form-item>
              <a-form-item>
                <a-button type="primary" @click="queryStatistics" :loading="statisticsLoading">
                  查询统计
                </a-button>
              </a-form-item>
              <a-form-item>
                <a-button @click="resetDate">重置</a-button>
              </a-form-item>
            </a-form>
          </div>
        </a-card>
      </div>

      <!-- 30天统计数据区域 -->
      <div class="statistics-section">
        <a-card size="small" title="30天过磅和过车数据统计" :loading="statisticsLoading">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="总过车次数"
                :value="statistics.totalVehicleCount"
                suffix="次"
                :value-style="{ color: '#1890ff' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="过磅次数"
                :value="statistics.totalWeightCount"
                suffix="次"
                :value-style="{ color: '#52c41a' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="超载次数"
                :value="statistics.overloadCount"
                suffix="次"
                :value-style="{ color: '#f5222d' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="超载率"
                :value="statistics.overloadRate"
                suffix="%"
                :precision="2"
                :value-style="{ color: statistics.overloadRate > 10 ? '#f5222d' : '#52c41a' }"
              />
            </a-col>
          </a-row>

          <a-divider />

          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="总重量"
                :value="statistics.totalWeight"
                suffix="吨"
                :precision="2"
                :value-style="{ color: '#722ed1' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="平均重量"
                :value="statistics.avgWeight"
                suffix="吨"
                :precision="2"
                :value-style="{ color: '#fa8c16' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="最大重量"
                :value="statistics.maxWeight"
                suffix="吨"
                :precision="2"
                :value-style="{ color: '#eb2f96' }"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="涉及车辆数"
                :value="statistics.uniqueVehicleCount"
                suffix="辆"
                :value-style="{ color: '#13c2c2' }"
              />
            </a-col>
          </a-row>
        </a-card>
      </div>

      <!-- 当日详细数据区域 -->
      <div class="daily-data-section">
        <a-card size="small" title="当日详细过磅数据">
          <template #extra>
            <a-space>
              <a-date-picker
                v-model:value="selectedDate"
                :format="dateFormat"
                placeholder="选择查询日期"
                @change="queryDailyData"
                style="width: 150px"
              />
              <span>共 {{ pagination.total }} 条数据</span>
              <a-button size="small" @click="exportDailyData" :loading="exportLoading">
                导出当日数据
              </a-button>
            </a-space>
          </template>

          <a-table
            :columns="columns"
            :data-source="dataSource"
            :loading="loading"
            :pagination="pagination"
            :scroll="{ x: 1500, y: 300 }"
            size="small"
            @change="handleTableChange"
            row-key="id"
          >
            <!-- 车牌号插槽 -->
            <template #vehicleNo="{ record }">
              <PlateShow :plateNumber="record.vehicleNo" :color="record.plateColor" />
            </template>

            <!-- 超载状态插槽 -->
            <template #overload="{ record }">
              <a-tag :color="record.isOverload == 1 ? 'red' : 'green'">
                {{ record.isOverload == 1 ? '超载' : '正常' }}
              </a-tag>
            </template>

            <!-- 重量插槽 -->
            <template #weight="{ record }">
              <span>{{ formatWeight(record.total) }}</span>
            </template>

            <!-- 操作插槽 -->
            <template #action="{ record }">
              <a-space>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  详情
                </a-button>
              </a-space>
            </template>
          </a-table>
        </a-card>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { PlateShow } from '/@/components/Plate';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import {
  generateMockWeightData,
  type WeightDataRecord,
  type WeightDataQueryParams
} from './api';

// 组件属性
const props = defineProps({
  title: {
    type: String,
    default: '过磅数据查询'
  }
});

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const statisticsLoading = ref(false);
const dateRange = ref<[Dayjs, Dayjs] | null>(null);
const dates = ref<[Dayjs, Dayjs] | null>(null);
const selectedDate = ref<Dayjs | null>(null);
const dataSource = ref<WeightDataRecord[]>([]);

// 统计数据
const statistics = reactive({
  totalVehicleCount: 0,    // 总过车次数
  totalWeightCount: 0,     // 过磅次数
  overloadCount: 0,        // 超载次数
  overloadRate: 0,         // 超载率
  totalWeight: 0,          // 总重量(吨)
  avgWeight: 0,            // 平均重量(吨)
  maxWeight: 0,            // 最大重量(吨)
  uniqueVehicleCount: 0,   // 涉及车辆数
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`,
});

// 日期格式
const dateFormat = 'YYYY-MM-DD';

// 模态框标题
const modalTitle = computed(() => props.title);

// 表格列配置
const columns = [
  {
    title: '检测单号',
    dataIndex: 'checkNo',
    width: 150,
    ellipsis: true,
  },
  {
    title: '站点名称',
    dataIndex: 'siteName',
    width: 120,
  },
  {
    title: '车牌号',
    dataIndex: 'vehicleNo',
    width: 120,
    slots: { customRender: 'vehicleNo' },
  },
  {
    title: '轴数',
    dataIndex: 'axles',
    width: 60,
  },
  {
    title: '轴型',
    dataIndex: 'axleType',
    width: 80,
  },
  {
    title: '重量',
    dataIndex: 'total',
    width: 100,
    slots: { customRender: 'weight' },
  },
  {
    title: '限重(吨)',
    dataIndex: 'limitWeight',
    width: 100,
    render: (text: number) => text ? (text / 1000).toFixed(2) : '-',
  },
  {
    title: '超载(吨)',
    dataIndex: 'overWeight',
    width: 100,
    render: (text: number) => text ? (text / 1000).toFixed(2) : '-',
  },
  {
    title: '超载率',
    dataIndex: 'overRate',
    width: 80,
    render: (text: string) => text ? `${text}%` : '-',
  },
  {
    title: '超载状态',
    dataIndex: 'isOverload',
    width: 100,
    slots: { customRender: 'overload' },
  },
  {
    title: '检测时间',
    dataIndex: 'checkTime',
    width: 150,
  },
  {
    title: '检测类型',
    dataIndex: 'checkType',
    width: 100,
  },
  {
    title: '磅型',
    dataIndex: 'poundType',
    width: 80,
  },
  {
    title: '操作',
    key: 'action',
    width: 80,
    fixed: 'right',
    slots: { customRender: 'action' },
  },
];

// 注册模态框
const [registerModal, { closeModal }] = useModalInner(async (data) => {
  // 保存传入的企业信息
  if (data) {
    enterpriseInfo.value = data;
  }
  // 初始化数据
  resetDate();
  // 初始化当前日期为今天
  selectedDate.value = dayjs();
  // 查询统计数据和当日数据
  await queryStatistics();
  await queryDailyData();
});

// 企业信息
const enterpriseInfo = ref<any>({});

// 禁用日期函数 - 限制选择范围不超过30天
const disabledDate = (current: Dayjs) => {
  if (!dates.value || !dates.value.length) {
    return false;
  }
  const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 30;
  const tooEarly = dates.value[1] && dates.value[1].diff(current, 'days') > 30;
  return tooEarly || tooLate;
};

// 日历变化事件
const onCalendarChange = (val: [Dayjs, Dayjs] | null) => {
  dates.value = val;
};

// 处理日期变化
const handleDateChange = (dates: [Dayjs, Dayjs] | null) => {
  if (dates && dates.length === 2) {
    const start = dayjs(dates[0]);
    const end = dayjs(dates[1]);
    const diff = end.diff(start, 'day');
    if (diff > 30) {
      message.error('选择的日期范围不能超过30天');
      dateRange.value = null;
      return;
    }
  }
  dateRange.value = dates;
};

// 重置日期
const resetDate = () => {
  // 默认选择最近7天
  const endDate = dayjs();
  const startDate = endDate.subtract(6, 'day');
  dateRange.value = [startDate, endDate];
  dates.value = null;
  selectedDate.value = dayjs(); // 重置当日选择为今天
};

// 查询30天统计数据
const queryStatistics = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    message.warning('请选择时间范围');
    return;
  }

  statisticsLoading.value = true;
  try {
    const params: WeightDataQueryParams = {
      checkTime_begin: dateRange.value[0].format('YYYY-MM-DD 00:00:00'),
      checkTime_end: dateRange.value[1].format('YYYY-MM-DD 23:59:59'),
      // 如果有企业信息，添加企业筛选条件
      ...(enterpriseInfo.value.enterpriseId && {
        enterpriseId: enterpriseInfo.value.enterpriseId,
        enterpriseName: enterpriseInfo.value.enterpriseName,
      }),
    };

    // 使用模拟数据进行开发测试
    // 生产环境中替换为: const result = await getWeightStatistics(params);
    const mockStatistics = generateMockStatistics(params);

    // 更新统计数据
    Object.assign(statistics, mockStatistics);

  } catch (error) {
    message.error('查询统计数据失败');
    console.error('查询统计数据失败:', error);
  } finally {
    statisticsLoading.value = false;
  }
};

// 查询当日详细数据
const queryDailyData = async () => {
  if (!selectedDate.value) {
    message.warning('请选择查询日期');
    return;
  }

  loading.value = true;
  try {
    const params: WeightDataQueryParams = {
      checkTime_begin: selectedDate.value.format('YYYY-MM-DD 00:00:00'),
      checkTime_end: selectedDate.value.format('YYYY-MM-DD 23:59:59'),
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
      column: 'checkTime',
      order: 'desc',
      // 如果有企业信息，添加企业筛选条件
      ...(enterpriseInfo.value.enterpriseId && {
        enterpriseId: enterpriseInfo.value.enterpriseId,
        enterpriseName: enterpriseInfo.value.enterpriseName,
      }),
    };

    // 使用模拟数据进行开发测试
    // 生产环境中替换为: const result = await getWeightDataList(params);
    const result = generateMockWeightData(params);

    if (result.success) {
      dataSource.value = result.result.records;
      pagination.total = result.result.total;
    } else {
      message.error(result.message || '查询当日数据失败');
    }

  } catch (error) {
    message.error('查询当日数据失败');
    console.error('查询当日数据失败:', error);
  } finally {
    loading.value = false;
  }
};



// 格式化重量显示
const formatWeight = (weight: number) => {
  if (!weight) return '-';
  return `${(weight / 1000).toFixed(2)}吨`;
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  queryData();
};

// 查看详情
const viewDetail = (record: WeightDataRecord) => {
  message.info(`查看 ${record.vehicleNo} 的详细信息`);
  // 这里可以打开详情弹框或跳转到详情页面
};

// 导出数据
const exportData = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    message.warning('请先选择时间范围');
    return;
  }

  exportLoading.value = true;
  try {
    // 生产环境中使用真实的导出API
    // const params: WeightDataQueryParams = {
    //   checkTime_begin: dateRange.value[0].format('YYYY-MM-DD 00:00:00'),
    //   checkTime_end: dateRange.value[1].format('YYYY-MM-DD 23:59:59'),
    // };
    // await exportWeightData(params);

    // 开发环境模拟导出成功
    await new Promise(resolve => setTimeout(resolve, 1000));
    message.success('导出成功');
  } catch (error) {
    message.error('导出失败');
    console.error('导出过磅数据失败:', error);
  } finally {
    exportLoading.value = false;
  }
};

// 确定按钮处理
const handleOk = () => {
  closeModal();
};

// 取消按钮处理
const handleCancel = () => {
  closeModal();
};
</script>

<style lang="less" scoped>
.weight-data-modal {
  .date-selector-section {
    margin-bottom: 16px;
  }
  
  .data-display-section {
    .ant-card {
      .ant-card-body {
        padding: 12px;
      }
    }
  }
}
</style>
