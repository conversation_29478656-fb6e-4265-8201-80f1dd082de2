<!-- DraggableRangeSlider.vue -->
<template>
  <div class="slider-wrapper">
    <!-- 显示起始点和终止点的名称 -->
    <div class="labels">
      <span class="label">{{ startLabel }}</span>
      <span class="label">{{ endLabel }}</span>
    </div>

    <!-- 滑块容器 -->
    <div
      ref="sliderContainer"
      class="slider-container"
      @mousedown="handleMouseDown"
      @touchstart="handleMouseDown"
    >
      <a-slider
        ref="sliderRef"
        v-model:value="range"
        :range="true"
        :min="min"
        :max="max"
        @change="onChange"
             :disabled="isDragging"
        :step="step"
        :style="{ position: 'relative' }"
      />
      <!-- 覆盖层，用于整体拖动 -->
      <div
        class="drag-overlay"
        :style="overlayStyle"
      ></div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Slider } from 'ant-design-vue';

export default {
  name: 'DraggableRangeSlider',
  components: {
    'a-slider': Slider,
  },
  props: {
    dataSource: {
      type: Array,
      required: true,
      // 数组元素应包含 'name' 属性
      // 示例: [{ name: 'Item 1' }, { name: 'Item 2' }, ...]
    },
    minStart:{
      type: Number,
      default: 0,
    },
  },
  setup(props,{emit}) {
    console.log(props,props.dataSource,props.minStart,'===>  ')
    // 定义滑块的最小值和最大值
    const min = 0;
    const max = computed(() => props.dataSource.length - 1); // 动态根据数组长度设置
    const maxRange = computed(() => props.dataSource.length - 1); // 动态根据数组长度设置

    const step = 1; // 步长

    // 定义滑块的当前范围
    const range = ref([0, props.minStart|| Math.min(10, props.dataSource.length - 1)]); // 初始范围，根据数据长度调整

    // 拖动状态
    const isDragging = ref(false);
    const startX = ref(0);
    const startRange = ref([0, props.minStart|| Math.min(10, props.dataSource.length - 1)]);

    // 引用滑块容器和滑块实例
    const sliderContainer = ref(null);
    const sliderRef = ref(null);

    // 滑块容器的宽度
    const sliderWidth = ref(0);

    // 计算覆盖层的样式，用于整体拖动
    const overlayStyle = computed(() => {
      if (!sliderContainer.value) return {};
      const rect = sliderContainer.value.getBoundingClientRect();
      const rangeWidth = (range.value[1] - range.value[0]) / (max.value - min);
      return {
        position: 'absolute',
        left: `${(range.value[0] - min) / (max.value - min) * 100}%`,
        width: `${rangeWidth * 100}%`,
        top: '16px',
        bottom: 0,
        cursor: 'move',
        height:'30px'
      };
    });

    // 计算起始点和终止点的名称
    const startLabel = computed(() => {
      const index = range.value[0];
      return props.dataSource[index]?.name || `Item ${index + 1}`;
    });

    const endLabel = computed(() => {
      const index = range.value[1];
      return props.dataSource[index]?.name || `Item ${index + 1}`;
    });

  
    // 更新滑块容器的宽度
    const updateSliderWidth = () => {
      if (sliderContainer.value) {
        sliderWidth.value = sliderContainer.value.getBoundingClientRect().width;
      }
    };

    onMounted(() => {
      updateSliderWidth();
      window.addEventListener('resize', updateSliderWidth);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', updateSliderWidth);
    });

    // 处理整体拖动的鼠标移动
    const handleMouseMove = (e) => {
      if (!isDragging.value) return;

      let clientX;
      if (e.type.startsWith('touch')) {
        clientX = e.touches[0].clientX;
      } else {
        clientX = e.clientX;
      }

      const deltaX = clientX - startX.value;
      const deltaValue = Math.round((deltaX / sliderWidth.value) * (max.value - min));

      let newStart = startRange.value[0] + deltaValue;
      let newEnd = startRange.value[1] + deltaValue;

      // 限制范围在 min 和 max 之间
      if (newStart < min) {
        newStart = min;
        newEnd = newStart + (startRange.value[1] - startRange.value[0]);
      }
      if (newEnd > max.value) {
        newEnd = max.value;
        newStart = newEnd - (startRange.value[1] - startRange.value[0]);
      }

      // 防止范围超出
      if (newStart < min) newStart = min;
      if (newEnd > max.value) newEnd = max.value;

      range.value = [newStart, newEnd];
    };

    // 处理整体拖动的鼠标抬起
    const handleMouseUp = () => {
      if (isDragging.value) {
        isDragging.value = false;
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
        window.removeEventListener('touchmove', handleMouseMove);
        window.removeEventListener('touchend', handleMouseUp);
           console.log(range.value,'===>range ')
           emit('dataslider',range.value)
      }
    };

    // 处理鼠标按下事件，判断是否在选中范围内整体拖动
    const handleMouseDown = (e) => {
      // 计算鼠标点击的位置
      let clientX;
      if (e.type === 'touchstart') {
        clientX = e.touches[0].clientX;
      } else {
        clientX = e.clientX;
      }

      const rect = sliderContainer.value.getBoundingClientRect();
      const clickX = clientX - rect.left;

      // 计算选中范围的位置
      const selectedStart = (range.value[0] - min) / (max.value - min) * rect.width;
      const selectedEnd = (range.value[1] - min) / (max.value - min) * rect.width;
      const selectedWidth = selectedEnd - selectedStart;

      const threshold = selectedWidth * 0.3; // 中间区域宽度阈值
 
        // 点击在中间区域，可以整体拖动
        isDragging.value = true;
        startX.value = clientX;
        startRange.value = [...range.value];

        // 添加全局事件监听
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
        window.addEventListener('touchmove', handleMouseMove, { passive: false });
        window.addEventListener('touchend', handleMouseUp);
     
    };

    // 处理滑块范围变化
    const onChange = (value) => {
      range.value = value;
      console.log(value,'===>value')
      emit('dataslider',range.value)
    };

    return {
      range,
      isDragging,
      handleMouseDown,
      sliderContainer,
      sliderRef,
      overlayStyle,
      onChange,
      min,
      max,
      step,
      startLabel,
      endLabel,
    };
  },
};
</script>

<style scoped>
.slider-wrapper {
  width: 600px;
  margin: 0 auto;
  user-select: none; /* 防止拖动时选中文字 */
}

.labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  color: #1890ff;
}

.slider-container {
  position: relative;
}

.drag-overlay {
  /* 覆盖层的样式已在脚本中通过计算属性设置 */
  background-color: rgba(204, 204, 204,0.3);
}

.range-display {
  text-align: center;
  margin-top: 20px;
  font-weight: bold;
}
</style>
