import { defHttp } from '/@/utils/http/axios';

enum Api {
  getCameraRecognitionRate = '/statistics/plate/getCameraRecognitionRate',
  getEnterpriseRecognitionRate = '/statistics/plate/getEnterpriseRecognitionRate',
  getRecognitionRateDetail = '/statistics/plate/getRecognitionRateDetail',
}

/**
 * 获取摄像头识别率统计
 * @param params 查询参数
 * @returns 
 */
export const getCameraRecognitionRate = (params) => 
  defHttp.get({ url: Api.getCameraRecognitionRate, params }, { isTransformResponse: false });

/**
 * 获取企业总识别率统计
 * @param params 查询参数
 * @returns 
 */
export const getEnterpriseRecognitionRate = (params) => 
  defHttp.get({ url: Api.getEnterpriseRecognitionRate, params }, { isTransformResponse: false });

/**
 * 获取识别率详细数据
 * @param params 查询参数
 * @returns 
 */
export const getRecognitionRateDetail = (params) => 
  defHttp.get({ url: Api.getRecognitionRateDetail, params }, { isTransformResponse: false });
