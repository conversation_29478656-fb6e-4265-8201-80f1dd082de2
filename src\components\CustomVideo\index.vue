<template>
  <div>
    <Modal v-model:open="open" title="视频" @ok="handleOk">
      <Video :src="dataUrl" width="700px" height="700px" @play="onPlay" @pause="onPause" controls> 您的浏览器不支持视频标签。</Video>
    </Modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import Modal from '../Modal/src/components/Modal';
import { message } from 'ant-design-vue';
  const open = ref<boolean>(false);
  const dataUrl = ref<string>('');

  const handleOk = (e: MouseEvent) => {
    console.log(e);
    open.value = false;
  };
  const openModal = (url) => {
    open.value = true;
    console.log(url, '===<url');
    if(url){
      dataUrl.value =url
    }else{
      message.error(`无视频链接`)
    }
  };
  const onPlay = (e) => {
    console.log('播放');
  };
  const onPause = () => {
    console.log('暂停');
  };

  defineExpose({
    openModal,
  });
</script>
