<template>
  <div class="editable-cell">
    <div v-if="editable" class="editable-cell-input-wrapper">
      <a-select :default-value="value" @change="handleChange" style="width: 100px">
        <a-select-option value="0" :disabled="value == '3'"> 正常 </a-select-option>
        <a-select-option value="3" :disabled="value == '3'"> 等待卸货 </a-select-option>
        <a-select-option value="4" :disabled="value == '4'"> 已卸货 </a-select-option>
        <a-select-option value="5" :disabled="value == '5'"> 超载 </a-select-option>
        <a-select-option value="6" :disabled="value == '6'"> 跳轴 </a-select-option>
        <a-select-option value="7" :disabled="value == '7'"> 小于一吨 </a-select-option>
        <a-select-option value="8" :disabled="value == '8'"> 大于一吨 </a-select-option>
        <a-select-option value="9" :disabled="value == '9'"> 其它 </a-select-option>
      </a-select>
      <a-icon type="check" class="editable-cell-icon-check" @click="check" />
      <a-icon type="close" class="editable-cell-icon-close" @click="cancel" />
    </div>
    <div v-else class="editable-cell-text-wrapper">
      <a-tag :color="color" style="font-size: 14px; padding: 3px 10px">
        {{ tranStatus(value) }}
      </a-tag>
      <a-icon type="edit" class="editable-cell-icon" @click="edit" v-if="records.passStatusType != 0" />
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, unref, watch } from 'vue';

const props = {
  text: { type: String, default: 0 },
  records: { type: Object, default: {} },
  dvalue: { type: String, default: '' },
};

export default defineComponent({
  name: 'EditTableOverloadSelect',
  inheritAttrs: false,
  props,
  emits: ['onChange'],
  setup(props, { emit }) {
    const editable = ref(false);
    const color = ref('#FF0000');
    const value = ref(props.text);
    const noChangeValue = ref(props.text)
    console.log(value,'===>valuethis')  

    tranTag(value);

    watch(value, (newValue) => {
      tranTag(newValue);
    });

    function cancel() {
      console.log(noChangeValue.value,'===>noChangeValue.value')
      value.value = noChangeValue.value
      editable.value = false;
    }
    function edit() {
      editable.value = true;
    }
    //检查状态
    function check() {
      editable.value = false;
      noChangeValue.value =value.value
      emit('change', value.value);
    }
    // 编辑状态
    function handleChange(e) {
      console.log('哈哈修改了1', e);
      value.value = e;
      console.log('哈哈修改了2', value.value);
    }
    //翻译状态
    function tranStatus(val) {
      switch (val) {
        case '0':
          return '正常';
        case '1':
          return '等待复磅';
        case '2':
          return '已复磅';
        case '3':
          return '等待卸货';
        case '4':
          return '已卸货';
        case '5':
          return '超载';
        case '6':
          return '跳轴';
        case '7':
          return '小于一吨';
        case '8':
          return '大于一吨';
        case '9':
          return '其他';
      }
    }
    // 翻译颜色
    function tranTag(val) {
      console.log(val, '===>val');
      switch (unref(val)) {
        case '0':
          color.value = '#8dc145';
          break;
        case '1':
          color.value = '#FFD700';
          break;
        case '2':
          color.value = '#00BFFF';
          break;
        case '3':
          color.value = '#FFA500';
          break;
        case '4':
          color.value = '#00ff00';
          break;
        case '5':
          color.value = '#FF0000';
          break;
        case '6':
          color.value = '#c586c0';
          break;
        case '7':
          color.value = '#000051';
          break;
        case '8':
          color.value = '#ce9178';
          break;
        case '9':
          color.value = '#32b6ff';
          break;
      }
    }

    return { tranStatus, edit, cancel, check, handleChange, color, value, editable };
  },
});
</script>
<style scoped>
.editable-cell {
  position: relative;
}

.editable-cell-input-wrapper,
.editable-cell-text-wrapper {
  padding-right: 20px;
}

.editable-cell-text-wrapper {
  padding: 5px 20px 5px 5px;
}

.editable-cell-icon {
  margin: 0 auto;
}

.editable-cell-icon,
.editable-cell-icon-check,
.editable-cell-icon-close {
  position: absolute;
  right: 0;
  width: 20px;
  cursor: pointer;
}

.editable-cell-icon {
  line-height: 30px;
  display: none !important;
}

.editable-cell-icon-check {
  line-height: 35px;
}

.editable-cell-icon-close {
  margin-top: 20px;
  line-height: 35px;
}

.editable-cell:hover .editable-cell-icon {
  display: inline-block !important;
}

.editable-cell-icon:hover,
.editable-cell-icon-check:hover {
  color: #108ee9;
}

.editable-add-btn {
  margin-bottom: 0px;
}
</style>
