# EnterpriseWeightModal 企业过磅数据统计组件

## 功能描述

这是一个专门为企业过磅数据统计分析设计的弹框组件，主要功能包括：

### 📊 30天统计图表展示
- **过磅次数趋势图**：显示30天内过磅次数和过车次数的对比趋势
- **超载率统计图**：展示30天内超载率的变化情况，支持堆叠柱状图显示
- **车辆类型分布图**：饼图展示不同轴数车辆的分布情况
- **重量分布统计图**：柱状图显示车辆重量分布，区分正常和超载

### 📈 智能数据生成
- 根据企业类型（煤矿/普通企业）生成不同特征的数据
- 考虑工作日/周末的数据差异
- 模拟月初月末检查严格程度的影响

### 📋 当日详细数据
- 今日过磅数据的详细表格展示
- 支持分页、排序、筛选
- 车牌号特殊显示，超载状态标签化

## 组件特性

- ✅ 基于 ECharts 5.4.2 的专业图表展示
- ✅ 响应式设计，支持全屏显示
- ✅ 智能数据模拟，区分企业类型
- ✅ 实时数据刷新和导出功能
- ✅ 完整的 TypeScript 类型支持

## 使用方法

### 1. 基本使用

```vue
<template>
  <div>
    <a-button @click="openModal">查看企业过磅统计</a-button>
    <EnterpriseWeightModal @register="registerModal" />
  </div>
</template>

<script setup>
import { useModal } from '/@/components/Modal';
import { EnterpriseWeightModal } from '/@/components/WeightDataModal';

const [registerModal, { openModal }] = useModal();

const openModal = () => {
  openModal(true, {
    enterpriseId: 'enterprise_001',
    enterpriseName: '山西煤炭集团有限公司',
    enterpriseCode: 'COAL001',
  });
};
</script>
```

### 2. 在企业列表中集成

```vue
<template>
  <BasicTable @register="registerTable">
    <template #bodyCell="{ column, record, text }">
      <template v-if="column.dataIndex === 'name'">
        <a-button type="link" @click="viewEnterpriseWeightData(record)">
          {{ text }}
        </a-button>
      </template>
    </template>
  </BasicTable>
  
  <EnterpriseWeightModal @register="registerWeightModal" />
</template>

<script setup>
const [registerWeightModal, { openModal: openWeightModal }] = useModal();

const viewEnterpriseWeightData = (record) => {
  openWeightModal(true, {
    enterpriseId: record.id,
    enterpriseName: record.name,
    enterpriseCode: record.code,
  });
};
</script>
```

## 数据特征

### 煤矿企业数据特征
- **过磅次数**：1500-2000次/30天
- **超载率**：15-25%
- **平均重量**：35-45吨
- **车辆类型**：重型车辆占比高
- **重量分布**：偏重型分布

### 普通企业数据特征
- **过磅次数**：800-1600次/30天
- **超载率**：5-13%
- **平均重量**：20-35吨
- **车辆类型**：轻型车辆占比高
- **重量分布**：偏轻型分布

## 图表配置

### 1. 过磅次数趋势图
```typescript
// 支持的配置
- 30天数据展示
- 过磅次数 vs 过车次数对比
- 工作日/周末数据差异
- 平滑曲线展示
```

### 2. 超载率统计图
```typescript
// 支持的配置
- 堆叠柱状图显示
- 超载率颜色分级（绿色<10%，黄色10-15%，红色>15%）
- 月初月末严格检查影响
```

### 3. 车辆类型分布图
```typescript
// 支持的配置
- 环形饼图展示
- 2-6轴货车 + 半挂车分类
- 根据企业类型调整分布比例
```

### 4. 重量分布统计图
```typescript
// 支持的配置
- 堆叠柱状图（正常重量 + 超载重量）
- 根据企业类型调整重量区间
- 颜色区分正常/超载
```

## API 接口

### 传入参数
```typescript
interface EnterpriseInfo {
  enterpriseId: string;     // 企业ID
  enterpriseName: string;   // 企业名称
  enterpriseCode?: string;  // 企业编码
}
```

### 统计数据结构
```typescript
interface StatisticsData {
  totalCount: number;       // 总过磅次数
  overloadCount: number;    // 超载次数
  avgWeight: number;        // 平均重量(吨)
  overloadRate: number;     // 超载率(%)
}
```

## 自定义配置

### 1. 修改图表主题
```typescript
// 在组件中修改 ECharts 主题
echarts.init(chartElement, 'dark'); // 使用暗色主题
```

### 2. 调整数据生成规则
```typescript
// 修改企业类型判断逻辑
const isCoalMine = enterpriseInfo.value.enterpriseName?.includes('煤') || 
                   enterpriseInfo.value.enterpriseName?.includes('矿') ||
                   enterpriseInfo.value.enterpriseCode?.startsWith('COAL');
```

### 3. 自定义图表配置
```typescript
// 可以修改各个图表的 option 配置
const option = {
  // 自定义配置
  backgroundColor: '#f5f5f5',
  animation: true,
  animationDuration: 1000,
  // ...
};
```

## 性能优化

1. **图表懒加载**：图表在弹框打开时才初始化
2. **内存管理**：组件卸载时自动销毁图表实例
3. **数据缓存**：可添加数据缓存机制避免重复计算
4. **响应式更新**：图表支持窗口大小变化自适应

## 注意事项

1. **ECharts 依赖**：确保项目已安装 echarts ^5.4.2
2. **数据格式**：重量数据以公斤为单位，显示时自动转换为吨
3. **企业类型识别**：通过企业名称关键字识别企业类型
4. **图表销毁**：组件卸载时会自动销毁所有图表实例

## 更新日志

- v1.0.0: 初始版本，包含4个统计图表和今日数据表格
- v1.1.0: 优化数据生成逻辑，增加企业类型区分
- v1.2.0: 完善图表配置，增加交互功能
