<!-- src/components/CustomMap.vue -->
<template>
  <div id="customMap" style="width: 100%; height: 500px"></div>
</template>

<script lang="ts">
  import { ref, watch, defineComponent, onMounted } from 'vue';
  import { load } from '@amap/amap-jsapi-loader';
  import Modal from '../Modal/src/components/Modal';
  import { Select, Input, Button, Form } from 'ant-design-vue';
  import { useCounterStore } from '@/store/modules/getMapData';

  export default defineComponent({
    name: 'NewMap',
    props: {
    longitude: { type: String, default: '' },
    latitude:{type: String, default: ''}
  },
    setup(props, { emit, expose }) {
      const map = ref<AMap.Map | null>(null);
      console.log(props, '==>props');
      const markers = ref<any[]>([[props.longitude, props.latitude]]);
      const center = ref([props.longitude, props.latitude]);
      // 初始化地图
      const initMap = async () => {
        try {
          await load({
          key: '3ba95f602bc77ee6d74a6e1a54b893d9', // 替换为您的高德地图API密钥
          version: '1.4.15',
        });
          map.value = new window.AMap.Map('customMap', {
            center: [props.longitude, props.latitude],
            zoom: 13,
            dragEnable: false, // 确保拖拽功能开启
            zoomEnable: false, // 确保缩放功能开启
          });
          markers.value.forEach((position) => {
            const marker = new window.AMap.Marker({
              position,
            });
            marker.setMap(map.value);
          });
        } catch (error) {
          console.error('高德地图加载失败:', error);
        }
      };
      onMounted(() => {
        initMap();
      });
    },
  });
</script>

<style scoped>
  #customMap {
    width: 100%;
    height: 500px;
  }

  .title:hover {
    cursor: pointer;
    color: #1890ff;
    font-weight: 600;
  }

  #my-panel {
    width: 300px;
    height: 300px;
  }
</style>
