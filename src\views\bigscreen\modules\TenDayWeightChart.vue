<template>
  <div class="container">
    <div class="container_box" style="background: #041451">
      <div class="header">
        <div class="title">10日内超载数据波动图</div>
      </div>
      <div ref="myEchart" class="chart"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import echarts from '@/utils/lib/echarts';
import moment from 'moment';
import { getSiteWeightData } from '/@/views/dashboard/Analysis/api';

const myEchart = ref();
let chart: any;

const data = ref({
  dates: [] as string[], // 近10日日期
  overloadData: [] as number[], // 超载数据
});

const initChart = () => {
  chart = echarts.init(myEchart.value);
  updateChart();
};

const updateChart = () => {
  const option = {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      formatter: '{b}<br/>超载数量: {c}辆'
    },
    grid: {
      left: '8%',
      right: '5%',
      bottom: '20%',
      top: '15%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data.value.dates,
      axisLabel: {
        color: '#fff',
        fontSize: 12,
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '超载数量(辆)',
      nameTextStyle: {
        color: '#fff',
        fontSize: 12
      },
      axisLabel: {
        color: '#fff',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#fff'
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255,255,255,0.1)'
        }
      }
    },
    series: [
      {
        name: '超载数据',
        type: 'line',
        data: data.value.overloadData,
        smooth: true,
        lineStyle: {
          color: '#ff6b6b',
          width: 3
        },
        itemStyle: {
          color: '#ff6b6b'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ff6b6b80' },
              { offset: 1, color: '#ff6b6b10' }
            ]
          }
        }
      }
    ]
  };

  chart.setOption(option);
};



// 调用真实接口获取数据
const fetchSiteWeightData = async () => {
  try {
    console.log('开始调用 /dashboard/siteWeight 接口...');
    const result = await getSiteWeightData({});
    console.log('接口调用成功，返回数据:', result);

    // 检查返回数据结构
    if (result && result.result && Array.isArray(result.result) && result.result.length > 0) {
      parseRealData(result.result);
    } else if (result && Array.isArray(result) && result.length > 0) {
      parseRealData(result);
    } else {
      console.log('接口返回数据为空，使用模拟数据');
      generateMockData();
    }
  } catch (error) {
    console.error('接口调用失败:', error);
    console.log('使用模拟数据');
    generateMockData();
  }
};

// 解析真实数据 - 根据您提供的数据结构
const parseRealData = (apiData: any[]) => {
  console.log('解析真实数据:', apiData);

  const dates: string[] = [];
  const overloadData: number[] = [];

  // 按日期排序，确保显示顺序正确
  const sortedData = apiData.sort((a, b) => new Date(a.statDate).getTime() - new Date(b.statDate).getTime());

  sortedData.forEach(item => {
    // 格式化日期为 MM-DD 格式
    const date = moment(item.statDate).format('MM-DD');
    dates.push(date);

    // 使用 overloadCount 作为超载数量
    overloadData.push(item.overloadCount || 0);
  });

  data.value.dates = dates;
  data.value.overloadData = overloadData;

  console.log('解析后的数据:', {
    dates: data.value.dates,
    overloadData: data.value.overloadData
  });

  // 数据更新后重新渲染图表
  if (chart) {
    updateChart();
  }
};

const generateMockData = () => {
  // 生成近10日的日期
  const dates: string[] = [];
  const overloadData: number[] = [];

  for (let i = 9; i >= 0; i--) {
    const date = moment().subtract(i, 'days').format('MM-DD');
    dates.push(date);

    // 模拟超载数据
    overloadData.push(Math.floor(Math.random() * 100) + 20);
  }

  data.value.dates = dates;
  data.value.overloadData = overloadData;
};

onMounted(() => {
  // 调用真实接口获取数据
  fetchSiteWeightData();
  initChart();

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize();
    }
  });
});
</script>

<style lang="less" scoped>
.container {
  position: relative;
  height: 100%;
  
  .container_box {
    width: 95%;
    height: 95%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    box-shadow: rgba(128, 128, 128, 0.3) 0px 0px 40px inset;
    border: 2px solid rgba(128, 128, 128, 0.3);
    
    .header {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 15px;

      .title {
        color: #fff;
        font-size: 15px;
        font-weight: 600;
      }
    }
    
    .chart {
      flex: 1;
      width: 100%;
    }
  }
}
</style>
