// src/utils/Area.js

// 如果您的 pcaa 数据从一个模块导出，可以直接导入
/**
 * 省市区（PCA）处理类
 */
export default class Area {
  /**
   * 构造器
   * @param {Object} [pcaaData] - PCA 数据。如果未提供，默认为导入的 pcaa
   */
  constructor(pcaaData = pcaa) {
    this.pcaa = pcaaData;
    this.all = this.processPcaa(this.pcaa);
  }

  /**
   * 处理 PCA 数据并将其转换为扁平数组
   * @param {Object} pcaa - PCA 数据
   * @returns {Array} - 扁平化的 PCA 数组
   */
  processPcaa(pcaa) {
    const arr = [];
    const provinces = pcaa['86'];

    Object.keys(provinces).forEach(provinceId => {
      arr.push({ id: provinceId, text: provinces[provinceId], pid: '86', index: 1 });
      const cities = pcaa[provinceId];

      Object.keys(cities).forEach(cityId => {
        arr.push({ id: cityId, text: cities[cityId], pid: provinceId, index: 2 });
        const areas = pcaa[cityId];

        if (areas) {
          Object.keys(areas).forEach(areaId => {
            arr.push({ id: areaId, text: areas[areaId], pid: cityId, index: 3 });
          });
        }
      });
    });

    return arr;
  }

  /**
   * 获取所有 PCA 数据
   * @returns {Array} - 扁平化的 PCA 数组
   */
  get pca() {
    return this.all;
  }

  /**
   * 根据名称获取代码
   * @param {string} text - 区域名称
   * @returns {string} - 对应的代码
   */
  getCode(text) {
    if (!text || text.length === 0) return '';
    const item = this.all.find(item => item.text === text);
    return item ? item.id : '';
  }

  /**
   * 根据代码获取完整名称（省/市/区）
   * @param {string} code - 区域代码
   * @returns {string} - 以 '/' 连接的完整名称
   */
  getText(code) {
    if (!code || code.length === 0) return '';
    const arr = [];
    this.getAreaByCode(code, arr, 3);
    return arr.join('/');
  }

  /**
   * 根据代码获取层级代码
   * @param {string} code - 区域代码
   * @returns {Array} - 层级代码数组
   */
  getRealCode(code) {
    const arr = [];
    this.getPcode(code, arr, 3);
    return arr;
  }

  /**
   * 递归获取父级代码
   * @param {string} id - 当前区域 ID
   * @param {Array} arr - 存储代码的数组
   * @param {number} index - 当前层级索引
   */
  getPcode(id, arr, index) {
    const item = this.all.find(item => item.id === id && item.index === index);
    if (item) {
      arr.unshift(id);
      if (item.pid !== '86' && index > 1) {
        this.getPcode(item.pid, arr, index - 1);
      }
    }
  }

  /**
   * 递归获取父级区域名称
   * @param {string} code - 当前区域代码
   * @param {Array} arr - 存储名称的数组
   * @param {number} index - 当前层级索引
   */
  getAreaByCode(code, arr, index) {
    const item = this.all.find(item => item.id === code && item.index === index);
    if (item) {
      arr.unshift(item.text);
      if (item.pid !== '86' && index > 1) {
        this.getAreaByCode(item.pid, arr, index - 1);
      }
    }
  }
}
