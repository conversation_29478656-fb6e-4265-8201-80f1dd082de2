<template>
  <div class="container">
    <div class="container_box" style="background: #041451">
      <div ref="myEchart" class="chart"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import moment from 'moment';
  import { onMounted, ref } from 'vue';
  import echarts from '@/utils/lib/echarts';
  import { getSiteStatisticData } from '../bigScreen.api';
  interface HighwayChart {
    options: any;
    monthList: any;
    checkNumList: any;
    overNumList: any;
    overRateList: any;
  }
  const myEchart = ref();

  const xData = ref(moment().format('YYYY'));
  const type = ref('month')
  let chart;
  const data = ref<HighwayChart>({
    options: {},
    monthList: [],
    checkNumList: [],
    overNumList: [],
    overRateList: [],
  });
  const initOptions = () => {
    data.value.options = {
      title: {
        text: '超限检测站称重数据统计',
        top: '10px',
        left: '10px',
        textStyle: {
          fontSize: '15',
          fontWeight: '600',
          color: '#fff',
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter: '月份{b0}<br/> {a0}:{c0}辆 <br/>{a1}:{c1}辆 <br/>{a2}:{c2}%',
      },
      legend: {
        left: 'center',
        bottom: '0px',
        data: ['检测数据', '超载数据', '超载率'],
        textStyle: {
          color: '#fff',
        },
      },
      grid: {
        left: '3%',
        right: '3%',
        bottom: '12%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
            },
            interval: 4,
          },
          axisLine: {
            show: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: { show: false }, //去除网格线
          axisLabel: {
            show: true,
            textStyle: {
              color: '#fff',
            },
          },
          axisLine: {
            show: true,
          },
        },
        {
          type: 'value',
          axisLabel: {
            formatter: (value) => {
              if (value) return `${value}%`;
              return value;
            },
            textStyle: {
              color: '#fff',
            },
          },
          nameLocation: 'center',
          nameGap: 35,
          nameRotate: 0,
          nameTextStyle: {
            fontSize: 16,
            color: '#293C55',
          },
          splitLine: { show: false }, //去除网格线
          min: 0,
          max: 1,
          interval: 50,
          // 设置轴线的属性
          axisLine: {
            lineStyle: {
              color: '#293C55',
              width: 2, //这里是为了突出显示加上的
            },
            show: true,
          },
        },
      ],
      series: [
        {
          name: '检测数据',
          type: 'bar',
          smooth: true, //变为曲线 默认false折线
          emphasis: {
            focus: 'series',
          },
          data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        },
        {
          name: '超载数据',
          type: 'bar',
          smooth: true, //变为曲线 默认false折线
          emphasis: {
            focus: 'series',
          },
          data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        },
        {
          name: '超载率',
          type: 'line',
          smooth: true, //变为曲线 默认false折线
          emphasis: {
            focus: 'series',
          },
          data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        },
      ],
    };
  };
  const initCharts = () => {
    chart = echarts.init(myEchart.value);
    chart.setOption(data.value.options);
        // 监听点击事件
    chart.on('click', function (params) {
      // // 判断是否点击了X轴的类目

      let lastChar = params.name.slice(0, -1);
      let endChar = params.name.slice(-1);
      if (endChar == '月') {
        xData.value = lastChar;
        type.value = 'day';
        getData();
      } else {
        xData.value = moment().format('YYYY');
        type.value = 'month';
        getData();
      }
    });
  };
  const getData = () => {
    let newdata = {
      monthList: [],
      checkNumList: [],
      overNumList: [],
      overRateList: [],
    };
    getSiteStatisticData({ xData: xData.value, type: type.value }).then((res) => {
      let list = res.result;
      list.forEach((item: string[]) => {
      newdata.monthList.push(item['xdata']);
        newdata.checkNumList.push(item['checkNum']);
        newdata.overNumList.push(item['overNum']);
        newdata.overRateList.push(item['overRate']);
      });
        data.value.options.xAxis[0].data = newdata.monthList;
        data.value.options.series[0].data = newdata.checkNumList;
        data.value.options.series[1].data = newdata.overNumList;
        data.value.options.series[2].data = newdata .overRateList;
        chart.setOption(data.value.options);
    });
  };
  onMounted(() => {
    initOptions();
    initCharts();
    getData();
  });
</script>

<style lang="less">
  .container {
    position: relative;

    // background: red;
    .container_box {
      width: 95%;
      height: 95%;
      margin: 0 auto;

      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
