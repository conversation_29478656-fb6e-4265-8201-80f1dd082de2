<!--字典下拉多选-->
<template>
  <a-select :value="arrayValue" @change="onChange" mode="multiple" :filter-option="filterOption" :disabled="disabled"
    :placeholder="placeholder" allowClear :getPopupContainer="getParentContainer">
    <a-select-option v-for="(item, index) in dictOptions" :key="index" :getPopupContainer="getParentContainer"
      :value="item.value">
      <span :class="[useDicColor && item.color ? 'colorText' : '']"
        :style="{ backgroundColor: `${useDicColor && item.color}` }">{{ item.text || item.label }}</span>
    </a-select-option>
  </a-select>
</template>
<script lang="ts">
import { computed, defineComponent, onMounted, ref, nextTick, watch } from 'vue';
import { useRuleFormItem } from '/@/hooks/component/useFormItem';
import { propTypes } from '/@/utils/propTypes';
import { useAttrs } from '/@/hooks/core/useAttrs';
import { getDictItems } from '/@/api/common/api';
import { setPopContainer } from '/@/utils';
import { useSelectData } from '@/store/modules/getMapData.ts'
export default defineComponent({
  name: 'CustomDictSelect',
  components: {},
  inheritAttrs: false,
  props: {
    value: propTypes.oneOfType([propTypes.string, propTypes.array]),
    placeholder: {
      type: String,
      default: '请选择',
      required: false,
    },
    readOnly: {
      type: Boolean,
      required: false,
      default: false,
    },
    options: {
      type: Array,
      default: () => [],
      required: false,
    },
    triggerChange: {
      type: Boolean,
      required: false,
      default: true,
    },
    spliter: {
      type: String,
      required: false,
      default: ',',
    },
    popContainer: {
      type: String,
      default: '',
      required: false,
    },
    dictCode: {
      type: String,
      required: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    useDicColor: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['options-change', 'change', 'input', 'update:value'],
  setup(props, { emit, }) {
    const emitData = ref<any[]>([]);
    const arrayValue = ref<any[]>(!props.value ? [] : props.value.split(props.spliter));
    const dictOptions = ref<any[]>([]);
    const attrs = useAttrs();
    const store = useSelectData()
    const [state, , , formItemContext] = useRuleFormItem(props, 'value', 'change', emitData);
    onMounted(() => {
      if (props.dictCode) {
        loadDictOptions();
      } else {
        dictOptions.value = props.options;
      }
    });
    watch(
      () => store.selectData,
      () => {
        console.log(store.selectData, '===>store.selectData')
        loadDictOptions(store.selectData);
      }
    )
    watch(
      () => props.dictCode,
      () => {
        if (props.dictCode) {
          loadDictOptions();
        } else {
          dictOptions.value = props.options;
        }
      }
    );
    watch(
      () => props.value,
      (val) => {
        if (!val) {
          arrayValue.value = [];
        } else {
          arrayValue.value = props.value.split(props.spliter);
        }
      }
    );
    //适用于 动态改变下拉选项的操作
    watch(() => props.options, () => {
      if (props.dictCode) {
      } else {
        dictOptions.value = props.options;
      }
    });
    function onChange(selectedValue) {
      if (props.triggerChange) {
        emit('change', selectedValue.join(props.spliter));
        emit('update:value', selectedValue.join(props.spliter));
      } else {
        emit('input', selectedValue.join(props.spliter));
        emit('update:value', selectedValue.join(props.spliter));
      }
      nextTick(() => {
        formItemContext?.onFieldChange();
      });
    }
    function getParentContainer(node) {
      if (!props.popContainer) {
        return node?.parentNode;
      } else {
        return setPopContainer(node, props.popContainer);
      }
    }
    // 根据字典code查询字典项
    function loadDictOptions(data: any = '') {
      let string = ''
      if(data.length >1){
        string = data[1]
      }
      console.log(props.dictCode, '==>props.dictCode')
      let temp = (string ? `${props.dictCode},dictCode=${string}` : props.dictCode) || '';
      if (temp.indexOf(',') > 0 && temp.indexOf(' ') > 0) {
        // 编码后 是不包含空格的
        temp = encodeURI(temp);
      }
      getDictItems(temp).then((res) => {
        if (res) {
          dictOptions.value = res.map((item) => ({ value: item.value, label: item.text, color: item.color }));
          console.info('res', dictOptions.value);
        } else {
          console.error('getDictItems error: : ', res);
          dictOptions.value = [];
        }
      });
    }
    function filterOption(input, option) {
      return option.children()[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }
    return {
      state,
      attrs,
      dictOptions,
      onChange,
      arrayValue,
      getParentContainer,
      filterOption,
    };
  },
});
</script>
<style scoped lang='less'>
.colorText {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  padding: 0 6px;
  border-radius: 8px;
  background-color: red;
  color: #fff;
  font-size: 12px;
}
</style>