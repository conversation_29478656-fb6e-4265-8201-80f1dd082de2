# WeightDataModal 过磅数据弹框组件

## 功能描述

这是一个用于查询和展示过磅数据的弹框组件，主要功能包括：

- 30天时间范围选择（限制最大选择范围不超过30天）
- 过磅数据表格展示
- 数据分页显示
- 数据导出功能
- 详情查看功能

## 组件特性

- ✅ 基于项目现有的 BasicModal 组件
- ✅ 集成了30天时间范围限制
- ✅ 响应式表格设计，支持横向滚动
- ✅ 车牌号特殊显示组件
- ✅ 超载状态标签显示
- ✅ 重量单位自动转换（公斤转吨）
- ✅ 支持全屏显示
- ✅ 支持自定义标题

## 使用方法

### 1. 基本使用

```vue
<template>
  <div>
    <a-button type="primary" @click="openModal">
      查看过磅数据
    </a-button>
    
    <WeightDataModal @register="registerModal" />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal';
import { WeightDataModal } from '/@/components/WeightDataModal';

const [registerModal, { openModal: openWeightModal }] = useModal();

const openModal = () => {
  openWeightModal(true, {
    // 可以传递初始化参数
  });
};
</script>
```

### 2. 自定义标题

```vue
<template>
  <WeightDataModal 
    @register="registerModal" 
    title="企业过磅数据查询"
  />
</template>
```

### 3. 在现有页面中集成

```vue
<template>
  <div>
    <!-- 其他页面内容 -->
    <a-button type="primary" @click="showWeightData">
      查看过磅数据
    </a-button>
    
    <!-- 过磅数据弹框 -->
    <WeightDataModal @register="registerWeightModal" />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal';
import { WeightDataModal } from '/@/components/WeightDataModal';

// 注册过磅数据弹框
const [registerWeightModal, { openModal: openWeightModal }] = useModal();

// 显示过磅数据
const showWeightData = () => {
  openWeightModal(true);
};
</script>
```

## 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| title | string | '过磅数据查询' | 弹框标题 |

## 数据结构

### WeightDataRecord 接口

```typescript
interface WeightDataRecord {
  id: string;           // 记录ID
  checkNo: string;      // 检测单号
  siteName: string;     // 站点名称
  vehicleNo: string;    // 车牌号
  plateColor: string;   // 车牌颜色
  total: number;        // 总重量（公斤）
  limitWeight: number;  // 限重（公斤）
  overWeight: number;   // 超载重量（公斤）
  overRate: string;     // 超载率（百分比）
  isOverload: number;   // 是否超载（1:超载, 0:正常）
  checkTime: string;    // 检测时间
  checkType: string;    // 检测类型
  poundType: string;    // 磅型
  axles: number;        // 轴数
  axleType: string;     // 轴型
}
```

## API 集成

目前组件使用模拟数据，实际使用时需要替换以下部分：

### 1. 查询数据API

在 `queryData` 方法中替换模拟数据调用：

```typescript
// 替换这部分代码
const mockData = generateMockData();
dataSource.value = mockData.records;
pagination.total = mockData.total;

// 为实际API调用
import { list } from '/@/api/weight/checkSiteData';
const result = await list(params);
dataSource.value = result.records;
pagination.total = result.total;
```

### 2. 导出数据API

在 `exportData` 方法中添加实际的导出API调用：

```typescript
// 添加实际的导出API
import { exportXls } from '/@/api/weight/checkSiteData';
await exportXls(params);
```

## 样式定制

组件使用了 Less 样式，可以通过以下方式进行定制：

```less
.weight-data-modal {
  .date-selector-section {
    margin-bottom: 16px;
    
    .ant-card {
      border-radius: 8px;
    }
  }
  
  .data-display-section {
    .ant-table-wrapper {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
      }
    }
  }
}
```

## 注意事项

1. **时间范围限制**：组件限制最大选择范围为30天，超过会提示错误
2. **数据格式**：重量数据需要以公斤为单位传入，组件会自动转换为吨显示
3. **车牌显示**：使用了项目的 PlateShow 组件来显示车牌
4. **权限控制**：如需要权限控制，可以在使用时添加 v-auth 指令

## 依赖组件

- BasicModal（项目基础弹框组件）
- PlateShow（车牌显示组件）
- ant-design-vue（UI组件库）

## 集成到现有页面

### 1. 在治超站数据页面中集成

```vue
<template>
  <div>
    <!-- 现有的治超站数据表格 -->
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="showWeightDataModal">
          查看过磅数据
        </a-button>
      </template>
    </BasicTable>

    <!-- 过磅数据弹框 -->
    <WeightDataModal @register="registerWeightModal" />
  </div>
</template>

<script lang="ts" setup>
import { useModal } from '/@/components/Modal';
import { WeightDataModal } from '/@/components/WeightDataModal';

const [registerWeightModal, { openModal: openWeightModal }] = useModal();

const showWeightDataModal = () => {
  openWeightModal(true);
};
</script>
```

### 2. 在大屏展示中集成

```vue
<template>
  <div class="bigscreen-container">
    <!-- 大屏其他组件 -->
    <div class="data-overview" @click="showDetailData">
      <span>点击查看详细数据</span>
    </div>

    <WeightDataModal
      @register="registerModal"
      title="实时过磅数据"
    />
  </div>
</template>
```

## 自定义配置

### 1. 修改表格列

可以通过修改组件内的 `columns` 配置来自定义表格列：

```typescript
// 在组件内部修改
const columns = [
  // 添加新列
  {
    title: '企业名称',
    dataIndex: 'enterpriseName',
    width: 150,
  },
  // 修改现有列
  {
    title: '车牌号码',  // 修改标题
    dataIndex: 'vehicleNo',
    width: 150,        // 修改宽度
    slots: { customRender: 'vehicleNo' },
  },
  // ... 其他列配置
];
```

### 2. 修改查询条件

可以扩展查询参数来支持更多筛选条件：

```vue
<template>
  <!-- 在日期选择区域添加更多筛选条件 -->
  <a-form layout="inline">
    <a-form-item label="时间范围">
      <a-range-picker v-model:value="dateRange" />
    </a-form-item>

    <!-- 添加站点筛选 -->
    <a-form-item label="站点">
      <a-select v-model:value="selectedSite" style="width: 200px">
        <a-select-option value="">全部站点</a-select-option>
        <a-select-option value="site1">治超站1</a-select-option>
      </a-select>
    </a-form-item>

    <!-- 添加超载状态筛选 -->
    <a-form-item label="超载状态">
      <a-select v-model:value="overloadStatus" style="width: 120px">
        <a-select-option value="">全部</a-select-option>
        <a-select-option value="1">超载</a-select-option>
        <a-select-option value="0">正常</a-select-option>
      </a-select>
    </a-form-item>
  </a-form>
</template>
```

## 性能优化建议

1. **虚拟滚动**: 对于大量数据，建议启用表格的虚拟滚动功能
2. **分页加载**: 合理设置分页大小，避免一次加载过多数据
3. **缓存策略**: 可以添加查询结果缓存，避免重复请求
4. **防抖处理**: 对搜索输入添加防抖处理

## 常见问题

### Q: 如何修改时间范围限制？
A: 修改 `disabledDate` 函数中的天数限制：
```typescript
const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 60; // 改为60天
```

### Q: 如何添加权限控制？
A: 在使用组件的地方添加权限指令：
```vue
<a-button v-auth="'weight:data:view'" @click="openModal">
  查看过磅数据
</a-button>
```

### Q: 如何自定义导出功能？
A: 修改 `exportData` 方法，调用实际的导出API：
```typescript
const exportData = async () => {
  // 调用实际的导出API
  const params = { /* 查询参数 */ };
  await exportWeightData(params);
};
```

## 更新日志

- v1.0.0: 初始版本，包含基本的查询和展示功能
- v1.1.0: 添加了API集成和类型定义
- v1.2.0: 优化了组件结构和错误处理
